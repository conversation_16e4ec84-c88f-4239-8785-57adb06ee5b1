<?php


namespace App\Repositories\Admin;


use App\Imports\DisciplineImport;
use App\Models\Fu\ActionLog;

use App\Models\Fu\Course;
use App\Models\Fu\Department;
use App\Models\Fu\Group;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\Ho\Fu\Term as FuTerm;
use App\Models\T7\Discipline;
use App\Models\T7\DisciplineStudent;
use App\Models\T7\GradeGroup;
use App\Models\T7\GradeSyllabus;
use App\Models\T7\SyllabusPlan;
use App\Models\Vhpt\SyllabusGrade;
use App\Repositories\BaseRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class CourseRepository extends BaseRepository
{
    public function getModel()
    {
        return Course::class;
    }


    public function index(Request $request) {
        $terms = Term::orderBy('id', 'desc')->get();
        $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')
            ->whereRaw('endday >= CURRENT_DATE')
            ->first();
        $departments = Department::orderBy('id', 'desc')->get();

        return view('admin_v1.course.index', [
            'terms' => $terms,
            'currentTerm' => $currentTerm,
            'departments' => $departments
        ]);
    }

    public function store(Request $request) {
        $request->validate([
            'term_id' => ['required'],
            'subject_id' => ['required'],
            'syllabus_id' => ['required'],
            'groups_name' => ['nullable', 'string'],
            // 'is_vhpt' => ['required'],
            // 'attendance_required' => ['required'],
            // 'grade_required' => ['required']
        ],[
            'required' => 'Không được bỏ trống trường :attribute ',
        ],[
            'term_id' => 'Kỳ',
            'subject_id' => 'Môn học',
            'syllabus_id' => "Khung chương trình",
        ]);
        
        $checkUnique = Course::where('term_id', $request->term_id)
            ->where('subject_id', $request->subject_id)
            ->where('syllabus_id', $request->syllabus_id)->exists();
        if($checkUnique) {
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Khóa học đã tồn tại'
                ]
            ]);
        }

        try {
            DB::beginTransaction();
            $now = date('Y-m-d H:i:s', time());
            $syllabus = GradeSyllabus::find($request->syllabus_id);
            $subject = Subject::find($request->subject_id);
            $term = Term::find($request->term_id);
            $inputInsert = [
                'lastmodifier_login' => auth()->user()->user_login,
                'subject_id' => $request->subject_id,
                'syllabus_id' => $request->syllabus_id,
                'syllabus_code' => $syllabus->syllabus_code,
                'subject_id' => $request->subject_id,
                'term_id' => $request->term_id,
                'psubject_name' => $subject->subject_name,
                'psubject_code' => $subject->subject_code,
                'num_of_credit' =>0,
                'attendance_required' =>  isset($request->attendance_required) ? 1 : 0,
                'grade_required' => isset($request->grade_required) ? 1 : 0,
                'pterm_name' => $term->term_name,
                'num_of_group' => 0,
                'is_started' => 0,
                'pull_insert' => $now,
                'pull_last_update' => $now,
                'syllabus_name' => $syllabus->syllabus_name
            ];

            ActionLog::create([
                'object'        => 'course',
                'auth'          => auth()->user()->user_login,
                'action'        => 'add',
                'description'   => "Thêm mới khóa học" ,
                'object_id'     => "",
                'data_changed'  => "",
                'ip'            => $request->getClientIp(),
            ]);

            $course = Course::create($inputInsert);

            // xử lý group
            $group_codes = explode(',', $request->groups_name);
            $group_codes = array_map(function ($item) {
                return trim($item);
            }, $group_codes);
            DB::commit();
            return redirect()->back()->with([
                'status' => [
                    'type' => 'success', 
                    'messages' => 'Thêm mới thành công'
                ]
            ]);

        } catch (\Throwable $th) {
            //throw $th;
            Log::error($th);
            DB::rollback();
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Thêm mới thất bại vui lòng kiểm tra lại dữ liệu hoặc báo với cán bộ IT.'
                ]
            ]);
        }
    }

    public function import(Request $request) {
        $request->validate([
            'term_id' => ['required'],
            'file' => ['required', 'mimes:xlsx']
        ], [
            'required' => "Không được bỏ trống :attribute",
            'mimes' => ":attribute không đúng định dạng"
        ], [
            'term_id' => "Kỳ",
            'file' => "File"
        ]);

        $file = $request->file('file');
        $data = $this->importDataFromFile($file);

        if(!$data) {
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Không có dữ liệu để import'
                ]
            ]);
        }
        
        $arrayDataCheck = $this->checkDataImport($data, $request->term_id);
        $data = $arrayDataCheck['data'];
        $error = $arrayDataCheck['error'];

        try {
            //code...
            DB::beginTransaction();
            $this->handleImportData($data, $request->term_id);
            DB::commit();
            if($error) {
                $errorMessageArr = array_map(function($item) {
                    return $item['message'];
                }, $error); 
                $errorMessage = implode(',<br />', $errorMessageArr);
                return redirect()->back()->with([
                    'status' => [
                        'type' => 'danger', 
                        'messages' => $errorMessage
                    ]
                ]);
            }else{
                return redirect()->back()->with([
                    'status' => [
                        'type' => 'success', 
                        'messages' => 'Import thành công'
                    ]
                ]);
            }
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollback();
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Import thất bại vui lòng kiểm tra lại dữ liệu hoặc báo với cán bộ IT.'
                ]
            ]);
        }
    }

    // Check data import
    protected function checkDataImport($data, $term_id) {
        $dataCheck = [];
        $error = [];
        foreach($data as $key => $item) {
            $psubject_code = $item[0];
            $syllabus_code = $item[1];

            // check subject_code
            $subjectCodeCheck = Subject::where('subject_code', $psubject_code)->exists();
            if(!$subjectCodeCheck) {
                $index = $key + 1;
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, không tồn tại subject_code" 
                ]; 
                continue;
            }
            // check syllabus_code
            $syllabusCodeCheck = GradeSyllabus::where('syllabus_code', $syllabus_code)
                ->where('subject_code', $psubject_code)
                ->where('duyet', 1)->exists();

            if(!$syllabusCodeCheck) {
                $index = $key + 1;
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, syllabus_code không tồn tại hoặt chưa duyệt" 
                ]; 
                continue;
            }

            // Check duplicate
            $isDuplicate = Course::where('psubject_code', $psubject_code)
                ->where('syllabus_code', $syllabus_code)->where('term_id', $term_id)->exists();

            if($isDuplicate) {
                $index = $key + 1;
                $error[] = [
                    'row' => $index,
                    'message' => "Dữ liệu dòng {$index}, Dữ liệu đã tồn tại" 
                ]; 
                continue;
            }

            $dataCheck[] = [
                'psubject_code' => $psubject_code,
                'syllabus_code' => $syllabus_code
            ];
        }

        return [
            'data' => $dataCheck,
            'error' => $error
        ];
    }

    // Import
    protected function handleImportData($data, $term_id) {
        $now = date('Y-m-d H:i:s', time());
        $term = Term::find($term_id);

        foreach($data as $item) {
            $syllabus = GradeSyllabus::where( 'syllabus_code' , $item['syllabus_code'])->first();
            $subject = Subject::where('subject_code' ,$item['psubject_code'])->first();
            $inputInsert[] = [
                'lastmodifier_login' => auth()->user()->user_login,
                'subject_id' => $subject->id,
                'syllabus_id' => $syllabus->id,
                'syllabus_code' => $syllabus->syllabus_code,
                'term_id' => $term->id,
                'psubject_name' => $subject->subject_name,
                'psubject_code' => $subject->subject_code,
                'num_of_credit' => 0,
                'attendance_required' =>  1 ,
                'grade_required' =>  1 ,
                'pterm_name' => $term->term_name,
                'num_of_group' => 0,
                'is_started' => 0,
                'pull_insert' => $now,
                'pull_last_update' => $now,
                'syllabus_name' => $syllabus->syllabus_name
            ];

        }

        ActionLog::create([
            'object'        => 'course',
            'auth'          => auth()->user()->user_login,
            'action'        => 'add',
            'description'   => "Import khóa học" ,
            'object_id'     => "",
            'data_changed'  => "",
            'ip'            => request()->getClientIp(),
        ]);

        $courses = Course::insert($inputInsert);

    }

    public function edit(Request $request, $id) {
        $course = Course::find($id);
        $groups = Group::where('body_id', $id)->get();

        $grade_groups = GradeGroup::with('grades')->where('syllabus_id', $course->syllabus_id)->get();
        $plans = SyllabusPlan::where('syllabus_id', $course->syllabus_id)->get();
        if(isset($request->subject_id)) {
            $syllabus = GradeSyllabus::where('subject_id', $request->subject_id)->get();
        }else{
            $syllabus = GradeSyllabus::where('subject_id', $course->subject_id)->get();
        }
        $subjects = Subject::all();
        return view('admin_v1.course.edit', [
            'course' => $course ,
            'groups' => $groups,
            'grade_groups' => $grade_groups,
            'plans' => $plans,
            'syllabus' => $syllabus,
            'subjects' => $subjects
        ]);
    }

    public function updateCourse($id, Request $request)
    {
        $course = Course::find($id);
        // dd($request->all());
        $groups = Group::where('body_id', $id)->get();
        try {
            DB::beginTransaction();

            if(isset($request->syllabus_id)) {
                $syllabus = GradeSyllabus::find($request->syllabus_id);
                if($request->syllabus_id != $course->syllabus_id) {
                    $checkUnique = Course::where('term_id', $course->term_id)
                        ->where('subject_id', $course->subject_id)
                        ->where('syllabus_id', $request->syllabus_id)->exists();
                    if($checkUnique) {
                        return redirect()->back()->with([
                            'status' => [
                                'type' => 'danger', 
                                'messages' => 'Khóa học đã tồn tại vui lòng chọn syllabus khác'
                            ]
                        ]);
                    }
                }

                if($syllabus){
                    $course->syllabus_id = $request->syllabus_id;
                    $course->syllabus_code = $syllabus->syllabus_code;
                    $course->syllabus_name = $syllabus->syllabus_name;
                }
            }
            if(isset($request->attendance_required)) {
                $attendance_required = $request->attendance_required == 'on' ? 1: 0;
                $course->attendance_required = $attendance_required;
            }else{
                $course->attendance_required = 0;
            }
            
            if(isset($request->grade_required)) {
                $grade_required = $request->grade_required == 'on' ? 1: 0;
                $course->grade_required = $grade_required;
            }else{
                $course->grade_required = 0;
            }

            ActionLog::create([
                'object'        => 'course',
                'auth'          => auth()->user()->user_login,
                'action'        => 'update',
                'description'   => "Cập nhập khóa học với id {$id}" ,
                'object_id'     => "",
                'data_changed'  => "",
                'ip'            => request()->getClientIp(),
            ]);

            $course->save();
            DB::commit();

            return redirect()->back()->with([
                'status' => [
                    'type' => 'success', 
                    'messages' => 'Chỉnh sửa thành công'
                ]
            ]);
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollback();
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger', 
                    'messages' => 'Cập nhập thất bại, lỗi hệ thống'
                ]
            ]);
        }
    }
}
