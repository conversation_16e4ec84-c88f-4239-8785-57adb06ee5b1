<template>
  <div class="w-100">
    <loading :active.sync="is_loading" :can-cancel="false" :is-full-page="true"></loading>
    <div class="row" v-show="showPreview">
      <div class="col-sm-12">
        <div class="card">
          <div class="card-body">
            <div class="content ql-editor" v-html="localInput"></div>
            <!-- Phải add class class="content ql-editor" -->
          </div>
        </div>
        <hr class="mt-2 mb-2" />
      </div>
    </div>
    <div class="row mt-3" v-show="showEditor">
      <div class="col-sm-12">
        <vue-editor :key="keyVueEditor" :editor-toolbar="customToolbar || {}" :editorOptions="editorSettings || {}"
          :useCustomImageHandler="true" @image-added="handleImageAdded" @image-removed="handleImageRemoved"
          v-model="localInput">
        </vue-editor>
      </div>
    </div>
    <div class="row mt-3 mb-6" v-show="!customDisplay">
      <div class="col-sm-3">
        <b-form-checkbox v-model="showPreview" name="check-button" switch>
          <b>Hiển thị xem trước</b>
        </b-form-checkbox>
        <!-- <div class="custom-control custom-switch">
          <input type="checkbox" class="custom-control-input" v-model="showPreview" id="showPreview" />
          <label class="custom-control-label" for="showPreview"></label>
        </div> -->
      </div>
      <div class="col-sm-3">
        <b-form-checkbox v-model="showEditor" name="check-button" switch>
          <b>Hiển thị editor</b>
        </b-form-checkbox>
        <!-- <div class="custom-control custom-switch">
          <input type="checkbox" class="custom-control-input" v-model="showEditor" id="showEditor" />
          <label class="custom-control-label" for="showEditor">Hiển thị editor</label>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
const FILE_DIR_SYSTEM = "custom-editor-upload";
const FILE_DIR = "default";
// Import component
import Loading from "vue-loading-overlay";
// Import stylesheet
import "vue-loading-overlay/dist/vue-loading.css";
import { VueEditor, Quill } from "vue2-editor";
import { ImageDrop } from "quill-image-drop-module";
import ImageResize from "quill-image-resize-module";
import quillTable from 'vue-quill-table';
function getFontName(font) {
  return font.toLowerCase().replace(/\s/g, "-");
}
const maxRows = 10;
const maxCols = 5;
const tableOptions = [];
for (let r = 1; r <= maxRows; r++) {
    for (let c = 1; c <= maxCols; c++) {
        tableOptions.push('newtable_' + r + '_' + c);
    }
}
const fontList = [
  "Arial",
  "Courier",
  "Garamond",
  "Tahoma",
  "Times New Roman",
  "Verdana",
];
const fontNames = fontList.map((font) => getFontName(font));
const fonts = Quill.import("formats/font");
fonts.whitelist = fontNames;
Quill.register(fonts, true);
Quill.register("modules/imageDrop", ImageDrop);
Quill.register("modules/imageResize", ImageResize);
Quill.register(quillTable.TableCell);
Quill.register(quillTable.TableRow);
Quill.register(quillTable.Table);
Quill.register(quillTable.Contain);
Quill.register('modules/table', quillTable.TableModule);

// Add fonts to CSS style
let fontStyles = "";
fontList.forEach(function (font) {
  let fontName = getFontName(font);
  fontStyles +=
    ".ql-snow .ql-picker.ql-font .ql-picker-label[data-value=" +
    fontName +
    "]::before, .ql-snow .ql-picker.ql-font .ql-picker-item[data-value=" +
    fontName +
    "]::before {" +
    "content: '" +
    font +
    "';" +
    "font-family: '" +
    font +
    "', sans-serif;" +
    "}" +
    ".ql-font-" +
    fontName +
    "{" +
    " font-family: '" +
    font +
    "', sans-serif;" +
    "}";
});
const node = document.createElement("style");
node.innerHTML = fontStyles;
document.body.appendChild(node);

const toBase64 = file => new Promise((resolve, reject) => {
  const reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = () => resolve(reader.result);
  reader.onerror = error => reject(error);
});
export default {
  props: {
    imageToBase64: {
      type: Boolean,
      default: false,
    },
    customDisplay: {
      type: Boolean,
      default: false,
      require: false,
    },
    fileDir: {
      type: String,
      default: FILE_DIR,
      require: false,
    },
    value: {
      type: String,
      default: "",
      require: false,
    },
  },
  components: {
    VueEditor,
    Loading,
  },
  data() {
    return {
      keyVueEditor: 0,
      showPreview: false,
      showEditor: true,
      is_loading: false,
      uploadData: {
        file: null,
        fileName: null,
        fileType: null,
        fileSize: null,
      },
      customToolbar: [
        [{ header: [false, 1, 2, 3, 4, 5, 6] }],
        ["bold", "italic", "underline", "strike"], // toggled buttons
        [
          { align: "" },
          { align: "center" },
          { align: "right" },
          { align: "justify" },
        ],
        ["blockquote", "code-block"],
        [{ list: "ordered" }, { list: "bullet" }, { list: "check" }],
        [{ indent: "-1" }, { indent: "+1" }], // outdent/indent
        [{ color: [] }, { background: [] }], // dropdown with defaults from theme
        ["link", "image", "video"],
        ["clean"], // remove formatting button
        [{ font: fonts.whitelist }],
        [
          { table: tableOptions },
          { table: 'append-row' },
          { table: 'append-col' },
        ],
      ],
      editorSettings: {
        modules: {
          imageDrop: true,
          imageResize: {},
          table: true,
        },
      },
    };
  },
  emits: ["update:value"],
  computed: {
    localInput: {
      get() {
        return this.value;
      },
      set(localInput) {
        this.$emit("input", localInput);
      },
    },
  },
  methods: {
    async parseToBase64(file) {
      return await toBase64(file);
    },
    /**
     * Upload ảnh lên server
     */
    handleImageAdded: function (file, Editor, cursorLocation, resetUploader) {
      if (this.imageToBase64) {
        this.importBase64(file, Editor, cursorLocation, resetUploader);
      } else {
        this.importImageFile(file, Editor, cursorLocation, resetUploader);
      }
    },
    /**
     * Upload ảnh định dạng base64
     */
    importBase64(file, Editor, cursorLocation, resetUploader) {
      this.parseToBase64(file).then(res => {
        Editor.insertEmbed(cursorLocation, "image", res);
        resetUploader();
        this.is_loading = false;
      })
    },
    /**
     * Upload ảnh định dạng file
     */
    importImageFile(file, Editor, cursorLocation, resetUploader) {
      const config = {
        headers: { "content-type": "multipart/form-data" },
      };
      let formData = new FormData();
      formData.append("file", file);
      formData.append("dir", this.fileDir);
      formData.append("s3", this.s3Storage);
      this.is_loading = true;
      window.axios
        .post("/api/v1/plugin/uploadFileToSource", formData, config)
        .then((result) => {
          if (result.error == false) {
            let url = result.url;
            Editor.insertEmbed(cursorLocation, "image", url);
            resetUploader();
          } else {
            alert("Lỗi upload file");
          }
          this.is_loading = false;
        })
        .catch((err) => {
          alert("Lỗi up ảnh! \n" + err);
          this.is_loading = false;
        });
    },
    /**
     * Xóa trên server khi xóa ảnh trên editor
     */
    handleImageRemoved(file, Editor, cursorLocation, resetUploader) {
      const url = new URL(file);
      var arr_path = url.pathname.split("/");
      var file_name = arr_path[arr_path.length - 1];
      file_name = this.fileDir + "/" + file_name;
      window.axios
        .delete(
          "/api/v1/plugin/deleteFileFromSource?file=" +
          encodeURIComponent(file_name)
        )
        .then((result) => {
          console.log(result);
        });
    },
  },
};
</script>