<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Notifications\Notifiable;
use App\Models\Sms\Account;
use Illuminate\Foundation\Auth\User as Authenticatable;
use App\Models\Sms\StudentSmsPermission;

class User extends Authenticatable
{
    const ROLE_ADMIN = 1;
    const LEGAL_ENTITY_UNIT = 0;
    const LEGAL_ENTITY_UNIVERSITY = 1;
    const LEGAL_ENTITY = [
        0 => 'Khối',
        1 => 'Trường'
    ];

    protected $table = 'user';
    public $timestamps = false;
    use Notifiable;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_surname',
        'user_middlename',
        'user_givenname',
        'user_login',
        'user_code',
        'old_user_code',
        'user_DOB',
        'user_email',
        'user_address',
        'user_telephone',
        'user_level',
        'study_status',
        'study_status_code',
        'google2fa_secret',
        'dantoc',
        'kithu',
        'curriculum_id',
        'cmt',
        'ngaycap',
        'noicap',
        'created_date',
        'brand_name',
        'gender',
        'grade',
        'Legal_entity',
        'learn_start_day',
        'current_period',
        'people_id',
        'total_debt_subject',
        'last_term_id',
        'grade_create',
        'alternative_email',
    ];


    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function fullname()
    {
        $first = $this->user_surname;
        $middle = $this->user_middlename;
        $last = $this->user_givenname;
        $full_name = '';
        if ($first != '') {
            $full_name .= $first;
        }
        if ($middle != '') {
            $full_name .= " $middle";
        }
        if ($last != '') {
            $full_name .= " $last";
        }
        return $full_name;
    }

    public function roles()
    {
        return $this->hasMany('App\Models\Dra\T1UserRole', 'user_login', 'user_login');
    }

    public function isRoleAdmin()
    {
        return in_array(self::ROLE_ADMIN, session('roles'));
    }

    public function courseResult()
    {
        return $this->hasMany('App\Models\T7\CourseResult', 'student_login', 'user_login');
    }

    public function getFullNameAttribute()
    {
        $first = $this->user_surname;
        $middle = $this->user_middlename;
        $last = $this->user_givenname;
        $full_name = '';
        if ($first != '') {
            $full_name .= $first;
        }
        if ($middle != '') {
            $full_name .= " $middle";
        }
        if ($last != '') {
            $full_name .= " $last";
        }
        return $full_name;
    }

    public function dob()
    {
        return Carbon::createFromDate($this->user_DOB);
    }

    public function getUserDOBAttribute($value)
    {
        if (!$value || $value == '0000-00-00') {
            return '';
        }
        return Carbon::parse($value)->format('d-m-Y');
    }

    public function curriculum()
    {
        return $this->belongsTo('App\Models\Dra\CurriCulum');
    }

    public function getNgayNhapHocAttribute()
    {
        return Carbon::createFromDate($this->created_date)->format('d-m-Y');
    }

    public function getNgayCapCmtAttribute()
    {
        return Carbon::createFromDate($this->ngaycap)->format('d-m-Y');
    }

    public function getTrangThaiHocAttribute()
    {
        $status = config('status')->trang_thai_hoc;
        return (object)$status[$this->study_status];
    }

    static public function getFullnameByCode($Usercode)
    {
        $data = self::query()
            ->select(['user_surname', 'user_middlename', 'user_givenname'])
            ->where('user_code', $Usercode)
            ->first();

        if (!$data) return "";

        $first = $data->user_surname;
        $middle = $data->user_middlename;
        $last = $data->user_givenname;
        $full_name = '';
        if ($first != '') {
            $full_name .= $first;
        }
        if ($middle != '') {
            $full_name .= " $middle";
        }
        if ($last != '') {
            $full_name .= " $last";
        }
        return $full_name;
    }
    public function getUserPermissions()
    {
        return $this->hasMany(StudentSmsPermission::class, 'user_login', 'user_login');
    }

    public function oldUser()
    {
        if ($this->old_user_code == null || $this->old_user_code == '') {
            return null;
        }

        return User::on($this->connection)
            ->where('user_code', 'LIKE', $this->old_user_code)
            ->first();
    }
    public function account()
    {
        return $this->hasMany(Account::class, 'student_code', 'student_code')->orderBy('created_on', 'DESC');
    }
}
