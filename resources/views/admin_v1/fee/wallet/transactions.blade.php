@extends('layouts.admin_v1.main')

@section('title', 'Lịch sử giao dịch')

@section('content')
<div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
    <div class="kt-portlet kt-portlet--mobile">
        <div class="kt-portlet__head kt-portlet__head--lg">
            <div class="kt-portlet__head-label">
                <span class="kt-portlet__head-icon">
                    <i class="kt-font-brand flaticon2-line-chart"></i>
                </span>
                <h3 class="kt-portlet__head-title">
                    Lịch sử giao dịch - {{ $wallet->user_code }}
                </h3>
            </div>
            <div class="kt-portlet__head-toolbar">
                <div class="kt-portlet__head-wrapper">
                    <div class="kt-portlet__head-actions">
                        <a href="{{ route('admin.wallet.list') }}" class="btn btn-clean btn-icon-sm">
                            <i class="la la-long-arrow-left"></i>
                            Quay lại
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="kt-portlet__body">
            <!-- Thông tin ví -->
            <div class="kt-section">
                <div class="kt-section__content">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered table-hover">
                                <tr>
                                    <th width="30%">Mã sinh viên</th>
                                    <td>{{ $wallet->user_code }}</td>
                                </tr>
                                <tr>
                                    <th>Tài khoản</th>
                                    <td>{{ $wallet->user_login }}</td>
                                </tr>
                                <tr>
                                    <th>Trạng thái</th>
                                    <td>
                                        @if($wallet->is_locked)
                                        <span class="kt-badge kt-badge--danger kt-badge--inline">Đã khóa</span>
                                        @else
                                        <span class="kt-badge kt-badge--success kt-badge--inline">Hoạt động</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered table-hover">
                                <tr>
                                    <th width="30%">Số dư hiện tại</th>
                                    <td><strong class="text-success">{{ number_format($wallet->balance) }} VNĐ</strong></td>
                                </tr>
                                <tr>
                                    <th>Ngày tạo ví</th>
                                    <td>{{ $wallet->created_at->format('d/m/Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Cập nhật cuối</th>
                                    <td>{{ $wallet->updated_at ? $wallet->updated_at->format('d/m/Y H:i') : 'Chưa cập nhật' }}</td>
                                </tr>
                                <tr>
                                    <th>Tổng giao dịch</th>
                                    <td><span class="badge badge-info">{{ $transactions->total() }}</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Danh sách giao dịch -->
            <div class="kt-separator kt-separator--border-dashed kt-separator--space-lg"></div>
            <h4>Lịch sử giao dịch</h4>

            <div class="table-responsive">
                <table class="table table-striped- table-bordered table-hover table-checkable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Ngày giao dịch</th>
                            <th>Loại ví</th>
                            <th>Loại giao dịch</th>
                            <th>Số tiền</th>
                            <th>Số dư trước</th>
                            <th>Số dư sau</th>
                            <th>Phương thức</th>
                            <th>Mã hóa đơn</th>
                            <th>Mô tả</th>
                            <th>Người thực hiện</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($transactions as $transaction)
                        <tr>
                            <td>{{ $transaction->id }}</td>
                            <td>{{ $transaction->created_at ? $transaction->created_at->format('d/m/Y H:i:s') : 'N/A' }}</td>
                            <td>
                                @switch($transaction->wallet_type)
                                    @case('main')
                                        Ví chính
                                        @break
                                    @case('study')
                                        Ví học phí
                                        @break
                                    @default
                                        Ví sinh viên
                                @endswitch
                            </td>
                            <td>
                                @switch($transaction->transaction_type)
                                    @case('deposit')
                                        <span class="kt-badge kt-badge--success kt-badge--inline">Nạp tiền</span>
                                        @break
                                    @case('withdraw')
                                        <span class="kt-badge kt-badge--warning kt-badge--inline">Rút tiền</span>
                                        @break
                                    @case('payment')
                                        <span class="kt-badge kt-badge--danger kt-badge--inline">Thanh toán</span>
                                        @break
                                    @case('create')
                                        <span class="kt-badge kt-badge--info kt-badge--inline">Tạo ví</span>
                                        @break
                                    @default
                                        <span class="kt-badge kt-badge--dark kt-badge--inline">Khác</span>
                                @endswitch
                            </td>
                            <td>
                                @if(in_array($transaction->transaction_type, ['deposit', 'transfer']))
                                <span class="text-success">+{{ number_format($transaction->amount) }} đ</span>
                                @else
                                <span class="text-danger">-{{ number_format($transaction->amount) }} đ</span>
                                @endif
                            </td>
                            <td>{{ number_format($transaction->balance_before) }} đ</td>
                            <td>{{ number_format($transaction->balance_after) }} đ</td>
                            <td>{{ $transaction->payment_method }}</td>
                            <td>{{ $transaction->invoice_id }}</td>
                            <td>{{ $transaction->description }}</td>
                            <td>{{ $transaction->created_by }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="kt-pagination kt-pagination--brand">
                <div class="kt-pagination__toolbar">
                    <span class="pagination__desc">
                        Hiển thị {{ $transactions->firstItem() }} - {{ $transactions->lastItem() }} của {{ $transactions->total() }} bản ghi
                    </span>
                </div>
                {{ $transactions->links() }}
            </div>
        </div>
    </div>
</div>
@endsection
