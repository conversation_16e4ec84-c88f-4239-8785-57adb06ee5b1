<?php

namespace App\Http\DataTables;

use DataTables;

use Illuminate\Support\Facades\DB;
use App\Models\Fu\Feedback;



class FeedbackdataTable extends DataTable
{
    /**
     * DataTable only columns
     */
    protected $only = [
        'feedback_id',
        'group',
        'subject',
        'teacher',
        'info',
        'created_by',
        'created_date',
        'status',
        'planer_login',
        'action'
    ];

    /**
     * DataTable raw columns
     */
    protected $columns = [
        'subject',
        'status',
        'info',
        'action'
    ];


    /**
     * Build datatable filter query
     */
    public function filter($query)
    {
        $openStatus = request()->input('open', null);
        $openerLogin = request()->input('open_user', null);
        $planerLogin = request()->input('plan_user', null);
        $blockCheck = request()->input('block_id', null);
        $termCheck = request()->input('term_id', null);
        $subjectCode = request()->input('subject_code', null);
        $subjectCodes = request()->input('subject_codes', []);
        $points = request()->input('points', []);
        $percent = request()->input('percent', []);
        $teacherLogin = request()->input('teacher_login', null);

        // check term
        if ($teacherLogin != null && $teacherLogin != "") {
            $query->where('list_group.teacher', $teacherLogin);
        }


        // check term
        if ($termCheck != null && $termCheck != -1) {
            $query->where('list_group.pterm_id', $termCheck);
        }

        // check block
        if ($blockCheck != null && $blockCheck != -1) {
            $query->where('list_group.block_id', $blockCheck);
        }

        if ($openerLogin != null && $openerLogin != -1) {
            $query->where('opener_login', $openerLogin);
        }

        if ($planerLogin != null && $planerLogin != -1) {
            $query->where('planer_login', $planerLogin);
        }

        if ($openStatus != null && $openStatus != -1) {
            $query->where('open', $openStatus);
        }

        if ($subjectCodes != null && is_array($subjectCodes) && count($subjectCodes) > 0) {
            $query->whereIn('psubject_code', $subjectCodes);
        }

        if ($points != null && is_array($points)) {
            $query->where('feedback.GPA', '>=', ($points[0] ?? 0));
            $query->where('feedback.GPA', '<=', ($points[1] ?? 4));
        }

        if ($percent != null && is_array($percent) && count($percent) > 1) {
            if ($percent[1] == 100) {
                $query->havingRaw(
                    'count(group_member.id) > 0
                    AND (feedback.hit / count(group_member.id)) >= ? ', 
                    [($percent[0] / 100)]
                );
            } else {
                $query->havingRaw(
                    'count(group_member.id) > 0
                    AND (
                        (feedback.hit / count(group_member.id)) >= ? 
                        AND (feedback.hit / count(group_member.id)) <= ?
                    )', 
                    [($percent[0] / 100), ($percent[1] / 100)]
                );
            }
        }

        // Check for filter:search
        if ($search = request()->input('search.value')) {
            $field = 'name';
            $listField = [
                'list_group.group_name',
                'feedback.teacher_login',
                'list_group.psubject_code',
                'feedback.opener_login',
            ];

            $query->where(function ($q) use ($listField, $field, $search) {
                $field = trim($field);
                $operator = 'like';
                foreach ($listField as $field) {
                    $q->orWhere($field, $operator, '%' . trim($search) . '%');
                }
            });
        }
    }


    /**
     * Build datatable data
     * @example $datatable->build()->toJson();
     */
    public function build()
    {
        /**
         * Build query for datatable
         */
        $query = Feedback::select([
            'list_group.id as group_id', // group id
            'list_group.group_name', // group name 
            'list_group.psubject_code', // mã môn
            'list_group.psubject_name', // mã lớp
            'list_group.start_date', // ngày bắt đầu học
            'list_group.pterm_id', // id kỳ
            'list_group.block_id', // id block
            'feedback.GPA', // số điểm đang có
            'feedback.id  as feedback_id', 
            'feedback.open as status', // trạng thái
            'feedback.opener_login as created_by', // người mở
            'feedback.planer_login', // Kế hoạch
            'feedback.open_day as created_date', // ngày mở
            'feedback.teacher_login as teacher', // giảng viên 
            'feedback.hit', // số lượt làm
            DB::raw('count(group_member.id) as number_student') // số lượng sinh viên
        ]) 
        ->leftJoin('group_member', 'group_member.groupid', '=', 'feedback.groupID')
        ->leftJoin('list_group', 'list_group.id', '=', 'feedback.groupID')
        ->groupBy('list_group.id')
        ->groupBy('feedback.id');

        $datatable = DataTables::of($query);
        $datatable->only($this->only);
        $datatable->rawColumns($this->columns)->setTotalRecords(100);

        /** ================== format Column ===================== */
        $datatable->addColumn('group', function ($node) {
            return "$node->group_name";
        });

        $datatable->addColumn('subject', function ($node) {
            return "<a href=" . route('admin.feedback.view', [
                'id' => $node->feedback_id,
            ]) . " target='_blank'>$node->psubject_code ($node->psubject_name)</a>";
        });

        $datatable->addColumn('info', function ($node) {
            $percent = 0;
            $style = "kt-font-success";
            if ($node->number_student != 0) {
                $percent = round((100 * ($node->hit / $node->number_student)), 2);
            }

            if ($percent < 60) {
                $style = "kt-font-danger";
            }

            return "<td class='text-center'>
            $node->hit / $node->number_student<br> <a href=" . route('admin.feedback.view', [
                'id' => $node->feedback_id,
            ]) . " target='_blank' title='Nhấn để xem chi tiết' class='$style kt-font-bold'>" . round($node->GPA, 2) . " (" . $percent . ")</a></td>";
        });

        $datatable->addColumn('status', function ($node) {
            return "<div class=\"custom-control custom-switch custom-switch-md\">
                <input data-id=\"$node->feedback_id\" type=\"checkbox\" class=\"custom-control-input change-status\" id=\"switch-$node->feedback_id\" " . ($node->status == 1 ? "checked" : "") . ">
                <label class=\"custom-control-label\" for=\"switch-$node->feedback_id\"></label>
            </div>";
        });


        $datatable->addColumn('action', function ($node) {
            return "<button class=\"btn btn-success btn-sync-feedback\" data-id=\"$node->feedback_id\" style=\"width: 100px;\">Đồng bộ</button>";
        });

        /** ================== Sort ===================== */
        $datatable ->orderColumn('info', function ($query, $order) {
            $query->having(DB::raw('count(group_member.id)'), '>', 0)
            ->orderBy(DB::raw('(feedback.hit / count(group_member.id))'), $order);
        });

        $datatable->orderColumn('group', function ($query, $order) {
            $query->orderBy('group_name', $order);
        });

        $datatable->orderColumn('subject', function ($query, $order) {
            $query->orderBy('psubject_code', $order);
        });

        $datatable->filter(function ($query) {
            return $this->filter($query);
        });
        return $datatable;
    }
}
