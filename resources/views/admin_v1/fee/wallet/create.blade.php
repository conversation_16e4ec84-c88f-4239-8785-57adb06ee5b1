@extends('layouts.admin_v1.main')

@section('title', 'Tạo ví sinh viên mới')

@section('content')
<div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
    <div class="row">
        <div class="col-lg-12">
            <!--begin::Portlet-->
            <div class="kt-portlet">
                <div class="kt-portlet__head">
                    <div class="kt-portlet__head-label">
                        <h3 class="kt-portlet__head-title">
                            Tạo ví sinh viên mới
                        </h3>
                    </div>
                </div>
                <!--begin::Form-->
                <form class="kt-form kt-form--label-right" method="POST" action="{{ route('admin.wallet.create') }}">
                    @csrf
                    <div class="kt-portlet__body">
                        <div class="form-group row">
                            <label class="col-form-label col-lg-3 col-sm-12">Mã sinh viên <span class="text-danger">*</span></label>
                            <div class="col-lg-6 col-md-9 col-sm-12">
                                <input type="text" class="form-control @error('user_code') is-invalid @enderror" name="user_code" placeholder="Nhập mã sinh viên" value="{{ old('user_code') }}" required>
                                @error('user_code')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <span class="form-text text-muted">Vui lòng nhập mã sinh viên</span>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-form-label col-lg-3 col-sm-12">Tài khoản <span class="text-danger">*</span></label>
                            <div class="col-lg-6 col-md-9 col-sm-12">
                                <input type="text" class="form-control @error('user_login') is-invalid @enderror" name="user_login" placeholder="Nhập tài khoản sinh viên" value="{{ old('user_login') }}" required>
                                @error('user_login')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <span class="form-text text-muted">Vui lòng nhập tài khoản sinh viên</span>
                            </div>
                        </div>
                    </div>
                    <div class="kt-portlet__foot">
                        <div class="kt-form__actions">
                            <div class="row">
                                <div class="col-lg-3 col-md-3"></div>
                                <div class="col-lg-6 col-md-9">
                                    <button type="submit" class="btn btn-success">Tạo ví</button>
                                    <a href="{{ route('admin.wallet.list') }}" class="btn btn-secondary">Quay lại</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!--end::Form-->
            </div>
            <!--end::Portlet-->
        </div>
    </div>
</div>
@endsection
