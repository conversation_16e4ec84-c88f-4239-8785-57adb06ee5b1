<?php

namespace App\Repositories\Admin;

use App\Http\Lib;
use App\Models\Fu\ActionLog;
use Carbon\Carbon;
use App\Models\Fu\Area;
use App\Models\Fu\Room;
use App\Models\Fu\Term;
use App\Models\Fu\Block;
use App\Models\Fu\GradeCreate;
use App\Models\Fu\RoomType;
use App\Models\Fu\Slot;
use App\Models\Fu\User;
use Illuminate\Support\Facades\Storage;
use App\Repositories\BaseRepository;
use App\Utils\ResponseBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class OtherManageRepository extends BaseRepository
{

    public function getModel()
    {
        return Area::class;
    }

    /**
     * get list data area
     * @return mixed $response
     */
    public function getListArea() {
        try {
            $listArea = Area::orderBy('id', 'asc')->get();

            return response()->json([
                'listArea' => $listArea
            ]);
        } catch (\Throwable $th) {
            Log::error("--------- start err getListArea ---------");
            Log::error($th);
            Log::error("--------- end err getListArea ---------");
            return response([], 500);
        }
    }

    /**
     * create a new Area
     * @param mixed $request
     * @return mixed $response
     */
    public function createdArea($request) {
        try {
            $area = new Area();
            $area->area_name = $request->area_name;
            if ($area->save()) {
                ActionLog::create([
                    'object'        => 'area',
                    'auth'          => auth()->user()->user_login,
                    'action'        => 'create',
                    'description'   => "Tài khoản " . auth()->user()->user_login . " tạo mới khu $area->area_name",
                    'object_id'     => ($parentUser->id ?? ""),
                    'data_changed'  => json_encode([
                        'area_name' => $area->area_name
                    ]),
                    'ip'            => $request->getClientIp(),
                ]);
                return response(['status' => 'Create success'], 200);
            }
        } catch (\Throwable $e) {
            Log::error('--start err createdArea --');
            Log::error($e);
            Log::error('--end err createdArea --');
            return response('Fail', 500);
        }
    }

    /**
     * get list data room
     * @param  mixed $request {searchData}
     * @return mixed $response
     */
    public function getListRoom(Request $request) {
        try {
            $room_query = Room::query();
            $room_query->join('area', 'area.id', '=', 'room.area_id');

            if(isset($request->is_offline_area) && $request->is_offline_area != "" && $request->is_offline_area != null && $request->is_offline_area == 1) {
                $room_query->where('area.is_offline_area', $request->is_offline_area);
            }

            if (isset($request->room_name)) {
                if ($request->room_name != null && $request->room_name != "") {
                    $room_query->where("room.room_name", 'like', '%' . $request->room_name . '%');
                }
            }

            if (isset($request->capacity_min)) {
                if ($request->capacity_min != null && $request->capacity_min != "") {
                    $room_query->where("room.capacity", '>', $request->capacity_min);
                }
            }

            if (isset($request->capacity_max)) {
                if ($request->capacity_max != null && $request->capacity_max != "") {
                    $room_query->where("room.capacity", '<', $request->capacity_max);
                }
            }

            if (isset($request->room_type)) {
                if ($request->room_type != null && $request->room_type != "") {
                    $room_query->where("room.room_type", '=', $request->room_type);
                }
            }

            if (isset($request->valid_from)) {
                if ($request->valid_from != null && $request->valid_from != "") {
                    $room_query->where("room.valid_from", '>', $request->valid_from);
                }
            }

            if (isset($request->area_id)) {
                if ($request->area_id != null && $request->area_id != "") {
                    $room_query->where("room.area_id", '=', $request->area_id);
                }
            }

            if (isset($request->is_internal)) {
                if ($request->is_internal != null && $request->is_internal != "") {
                    $room_query->where("room.is_internal", '=', $request->is_internal);
                }
            }

            $room_query->where('is_deleted','=',0)->select([
                'room.id',
                'area_name',
                'is_offline_area',
                'area_id',
                'room_name',
                'room_type',
                'description',
                'capacity',
                'is_internal',
                'valid_from',
                'is_deleted'
            ])->orderBy('id', 'desc');
            if (isset($request->all) && $request->all == 1) {
                $room = $room_query->get();
            }else{
                $room = $room_query->paginate(10);
                dd($room);
                
            }
            $types = RoomType::get();

            return response(['rooms' => $room, 'room_types' => $types], 200);
        } catch (\Throwable $e) {
            Log::error('--start err getListRoom --');
            Log::error($e);
            Log::error('--end err getListRoom --');
            return response([], 500);
        }
    }


    public function getListRoomByType(Request $request) {
        $now = Carbon::now()->format('Y-m-d');
        $rooms = Room::select([
                'id',
                'area_id',
                'room_name',
                'capacity',
                'room_type',
                'is_internal',
                'valid_from',
                'description'
            ])
            ->where('is_deleted', 0)
            ->where('valid_from' , '<', $now)
            ->orderBy('id', 'desc')
            ->get();

        $types = RoomType::get();

        $listRoomByType = [];
        foreach ($types as $type) {
            $listRoomByType[$type->code] = [];
            foreach ($rooms as $room) {
                if ($room->room_type == $type->id) {
                    $listRoomByType[$type->code][] = $room;
                }
            }
        }

        return ResponseBuilder::Success($listRoomByType, "Thành công!");
    }

    /**
     * created a new room
     *
     * @param  mixed $request {area_id, room_name, capacity, room_type, is_internal, valid_from, description}
     * @return mixed $response
     */
    public function createdRoom(Request $request)
    {
        try {
            $room = new Room();
            $room->area_id = $request->area_id;
            $room->room_name = $request->room_name;
            $room->capacity = $request->capacity;
            $room->room_type = $request->room_type;
            $room->is_internal = $request->is_internal == null ? 0 : 1;
            $room->valid_from = $request->valid_from;
            $room->description = $request->description;
            if ($room->save()) {
                ActionLog::create([
                    'object'        => 'room',
                    'auth'          => auth()->user()->user_login,
                    'action'        => 'create',
                    'description'   => "Tài khoản " . auth()->user()->user_login . " tạo mới phòng $room->room_name",
                    'object_id'     => ($parentUser->id ?? ""),
                    'data_changed'  => json_encode([
                        'area_id' => $room->area_id,
                        'room_name' => $room->room_name,
                        'capacity' => $room->capacity,
                        'room_type' => $room->room_type,
                        'is_internal' => $room->is_internal,
                        'valid_from' => $room->valid_from,
                        'description' => $room->description
                    ]),
                    'ip'            => $request->getClientIp(),
                ]);
                return response(['status' => 'Create success'], 200);
            }
        } catch (\Throwable $e) {
            Log::error('--start err createdRoom --');
            Log::error($e);
            Log::error('--end err createdRoom --');
            return response('Fail', 500);
        }
    }

    /**
     * edit room
     *
     * @param  mixed $request {area_id, room_name, capacity, room_type, is_internal, valid_from, description}
     * @param  mixed $id {id room}
     * @return mixed $response
     */
    public function editRoom(Request $request, $id)
    {
        try {
            $room = Room::findOrFail($id);
            $room->area_id = $request->area_id;
            $room->room_name = $request->room_name;
            $room->capacity = $request->capacity;
            $room->room_type = $request->room_type;
            $room->is_internal = $request->is_internal == null ? 0 : 1;
            $room->valid_from = $request->valid_from;
            $room->description = $request->description;
            $room->save();
            ActionLog::create([
                'object'        => 'room',
                'auth'          => auth()->user()->user_login,
                'action'        => 'update',
                'description'   => "Tài khoản " . auth()->user()->user_login . " chỉnh sửa phòng $room->room_name",
                'object_id'     => ($parentUser->id ?? ""),
                'data_changed'  => json_encode([
                    'area_id' => $room->area_id,
                    'room_name' => $room->room_name,
                    'capacity' => $room->capacity,
                    'room_type' => $room->room_type,
                    'is_internal' => $room->is_internal,
                    'valid_from' => $room->valid_from,
                    'description' => $room->description
                ]),
                'ip'            => $request->getClientIp(),
            ]);
            return response(['status' => 'Update Success'], 200);
        } catch (\Throwable $e) {
            return response('Fail', 500);
        }
    }

    /**
     * delete room
     *
     * @param  integer $id {id room}
     * @return mixed $response
     */
    public function deleteRoom($id)
    {
        $room = Room::findOrFail($id);
        $room->delete();
        return response(['status' => 'Success'], 200);
    }

    /**
     * get list data term
     * @param  mixed $request {searchData}
     * @return mixed $response
     */
    public function getListTerm(Request $request) {
        try {
            $term_query = Term::query();

            if (isset($request->term_name)) {
                if ($request->term_name != null && $request->term_name != "") {
                    $term_query->where("term.term_name", 'like', '%' . $request->term_name . '%');
                }
            }

            if (isset($request->startday)) {
                if ($request->startday != null && $request->startday != "") {
                    $term_query->where("term.startday", '>', $request->startday);
                }
            }

            if (isset($request->endday)) {
                if ($request->endday != null && $request->endday != "") {
                    $term_query->where("term.endday", '<', $request->endday);
                }
            }
            // inner join table block
            $term = $term_query->select([
                'id',
                'campus_id',
                'term_name',
                'ordering',
                'startday',
                'endday',
                'is_locked',
                'lock_import',
                'lock_import_vb',
            ])->with('block')->orderBy('id', 'desc')->paginate(10);
            return response($term, 200);
        } catch (\Throwable $e) {
            Log::error('--start err getListTerm --');
            Log::error($e);
            Log::error('--end err getListTerm --');
            return response([], 500);
        }
    }

        /**
     * created a new block
     *
     * @param  mixed $request {area_id, room_name, capacity, room_type, is_internal, valid_from, description}
     * @return mixed $response
     */
    public function createdBlock(Request $request) {
        try {
            $block = new Block();
            $block->term_id = $request->term_id;
            $block->block_name = $request->block_name;
            $block->start_day = $request->start_day;
            $block->end_day = $request->end_day;
            if ($block->save()) {
                ActionLog::create([
                    'object'        => 'block',
                    'auth'          => auth()->user()->user_login,
                    'action'        => 'create',
                    'description'   => "Tài khoản " . auth()->user()->user_login . " tạo mới học phần $block->block_name",
                    'object_id'     => ($parentUser->id ?? ""),
                    'data_changed'  => json_encode([
                        'term_id' => $block->term_id,
                        'block_name' => $block->block_name,
                        'start_day' => $block->start_day,
                        'end_day' => $block->end_day
                    ]),
                    'ip'            => $request->getClientIp(),
                ]);
                return response(['status' => 'Create success'], 200);
            }
        } catch (\Throwable $e) {
            Log::error('--start err createdBLock --');
            Log::error($e);
            Log::error('--end err createdBLock --');
            return response('Fail', 500);
        }
    }

    /**
     * edit block
     *
     * @param  mixed $request {term_id, block_name, start_day, end_day}
     * @param  mixed $id {id block}
     * @return mixed $response
     */
    public function editBlock(Request $request, $id) {
        try {
            $block = Block::findOrFail($id);
            $block->term_id = $request->term_id;
            $block->block_name = $request->block_name;
            $block->start_day = $request->start_day;
            $block->end_day = $request->end_day;
            $block->save();
            ActionLog::create([
                'object'        => 'block',
                'auth'          => auth()->user()->user_login,
                'action'        => 'update',
                'description'   => "Tài khoản " . auth()->user()->user_login . " chỉnh sửa học phần $block->block_name",
                'object_id'     => ($parentUser->id ?? ""),
                'data_changed'  => json_encode([
                    'term_id' => $block->term_id,
                    'block_name' => $block->block_name,
                    'start_day' => $block->start_day,
                    'end_day' => $block->end_day
                ]),
                'ip'            => $request->getClientIp(),
            ]);
            return response(['status' => 'Update Success'], 200);
        } catch (\Throwable $e) {
            Log::error('--start err editBLock --');
            Log::error($e);
            Log::error('--end err editBLock --');
            return response('Fail', 500);
        }
    }

    /**
     * delete block
     *
     * @param  integer $id {id room}
     * @return mixed $response
     */
    public function deleteBlock($id) {
        try {
            $block = Block::findOrFail($id);
            $block->delete();
            ActionLog::create([
                'object'        => 'block',
                'auth'          => auth()->user()->user_login,
                'action'        => 'delete',
                'description'   => "Tài khoản " . auth()->user()->user_login . " xóa học phần $block->block_name",
                'object_id'     => ($parentUser->id ?? ""),
                'data_changed'  => json_encode([
                    'term_id' => $block->term_id,
                    'block_name' => $block->block_name,
                    'start_day' => $block->start_day,
                    'end_day' => $block->end_day
                ]),
                'ip'            => request()->getClientIp(),
            ]);
            return response(['status' => 'Success'], 200);
        } catch (\Throwable $e) {
            Log::error('--start err deleteBlock --');
            Log::error($e);
            Log::error('--end err deleteBlock --');
            return response('Thất bại! Có lỗi xảy ra.', 500);
        }
    }

    /**
     * get list data slot
     * @return mixed $response
     */
    public function getListSlot() {
        try {
            $listSlot = Slot::orderBy('slot_id', 'asc')->get();
            return response()->json([
                'listSlot' => $listSlot
            ]);
        } catch (\Throwable $th) {
            Log::error("--------- start err getListSlot ---------");
            Log::error($th);
            Log::error("--------- end err getListSlot ---------");
            return response([], 500);
        }
    }

    /**
     * edit slot
     *
     * @param  mixed $request {slot_id, slot_start, slot_end}
     * @param  mixed $id {id slot}
     * @return mixed $response
     */
    public function editSlot(Request $request, $id) {
        try {
            $rules = [
                'slot' => 'required,integer',
                'slot_start' => 'required',
                'slot_end' => 'required',
            ];
            $messages = [
                'slot.required' => 'Tên ca học không được bỏ trống',
                'slot.integer' => 'Tên ca học không hợp lệ',
                'slot_start.required' => 'Thời gian bắt đầu không được bỏ trống',
                'slot_end.required' => 'Thời gian kết thúc không được bỏ trống',
            ];
            $validator = Validator::make($request->all(), $rules, $messages);
            if ($validator->fails()) {
                $log = $validator->errors()->first();
                return ResponseBuilder::Fail($log);
            }
            $slot = Slot::findOrFail($id);
            $slot->slot_start= $request->slot_start;
            $slot->slot_end= $request->slot_end;
            $slot->save();
            ActionLog::create([
                'object'        => 'slot',
                'auth'          => auth()->user()->user_login,
                'action'        => 'update',
                'description'   => "Tài khoản " . auth()->user()->user_login . " chỉnh sửa ca học $slot->slot",
                'object_id'     => ($parentUser->id ?? ""),
                'data_changed'  => json_encode([
                    'slot_start' => $slot->slot_start,
                    'slot_end' => $slot->slot_end
                ]),
                'ip'            => $request->getClientIp(),
            ]);
            return response(['status' => 'Update Success'], 200);
        } catch (\Throwable $e) {
            Log::error('--start err editSlot --');
            Log::error($e);
            Log::error('--end err editSlot --');
            return response('Fail', 500);
        }
    }

    /**
     * edit slot
     *
     * @param  mixed $request {slot, slot_start, slot_end}
     * @return mixed $response
     */
    public function createSlot(Request $request) {
        try {
            $rules = [
                'slot_id' => 'required|integer',
                'slot_start' => 'required|date_format:H:i',
                'slot_end' => 'required|date_format:H:i|after:slot_start',
            ];
            $messages = [
                'slot_id.required' => 'Tên ca học không được bỏ trống',
                'slot_id.integer' => 'Tên ca học không hợp lệ',
                'slot_start.required' => 'Thời gian bắt đầu không được bỏ trống',
                'slot_end.required' => 'Thời gian kết thúc không được bỏ trống',
            ];
            $validator = Validator::make($request->all(), $rules, $messages);
            if ($validator->fails()) {
                $log = $validator->errors()->first();
                return ResponseBuilder::Fail($log);
            }
            $slot = Slot::create([
                'slot_id' => $request->slot_id,
                'slot_start' => $request->slot_start,
                'slot_end' => $request->slot_end,
            ]);
            ActionLog::create([
                'object'        => 'slot',
                'auth'          => auth()->user()->user_login,
                'action'        => 'create',
                'description'   => "Tài khoản " . auth()->user()->user_login . " tạo mới ca học $slot->slot",
                'object_id'     => ($parentUser->id ?? ""),
                'data_changed'  => json_encode([
                    'slot_start' => $slot->slot_start,
                    'slot_end' => $slot->slot_end
                ]),
                'ip'            => $request->getClientIp(),
            ]);
            return ResponseBuilder::Success($slot, "Thêm mới ca học thành công!");
        } catch (\Throwable $e) {
            Log::error('--start err editSlot --');
            Log::error($e);
            Log::error('--end err editSlot --');
            return ResponseBuilder::Fail("Thêm mới ca học thất bại!", null, 500);
        }
    }

    public function deleteSlot($id) {
        try {
            $slot = Slot::findOrFail($id);
            $slot->delete();
            ActionLog::create([
                'object'        => 'slot',
                'auth'          => auth()->user()->user_login,
                'action'        => 'delete',
                'description'   => "Tài khoản " . auth()->user()->user_login . " xóa ca học $slot->slot",
                'object_id'     => ($parentUser->id ?? ""),
                'data_changed'  => json_encode([
                    'slot_start' => $slot->slot_start,
                    'slot_end' => $slot->slot_end
                ]),
                'ip'            => request()->getClientIp(),
            ]);
            return ResponseBuilder::Success($slot, "Xoá ca học thành công!");
        } catch (\Throwable $e) {
            Log::error('--start err deleteSlot --');
            Log::error($e);
            Log::error('--end err deleteSlot --');
            return response('Thất bại! Có lỗi xảy ra.', 500);
        }
    }
    /**
     * @todo Tạo kỳ
     * <AUTHOR>
     * @since 02/04/0205
     *
     * @return void
     *
     */
    function createTerm($request) {
        $rules = [
            'term_name' => 'required',
            'end_day' => 'required|date_format:Y-m-d',
        ];

        $messages = [
            'term_name.required' => 'Tên kỳ không được bỏ trống',
            'end_day.required' => 'Ngày kết thúc không được bỏ trống',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            $mess = $validator->errors()->first();
            return ResponseBuilder::fail($mess);
        }

        $termName = trim(strip_tags($request->term_name));
        $checkTerm = Term::where('term_name', $termName)->first();
        if ($checkTerm) {
            return ResponseBuilder::fail("Tên kỳ đã tồn tại, vui lòng thử lại!");
        }

        // lấy kỳ cuối cùng và lấy ngày kết thúc của kỳ
        $termCheck = Term::orderBy('id', 'DESC')->first();
        $ordering = $termCheck->ordering;
        $lastDayTermCheck = Carbon::createFromFormat('Y-m-d', $termCheck->endday);
        $endDay = Carbon::createFromFormat('Y-m-d', $request->end_day);

        if ($endDay < now()) {
            return ResponseBuilder::fail("Ngày kết thúc không hợp lệ vì nhỏ hơn ngày hiện tại (" . now()->format('d/m/Y') . "), vui lòng thử lại!");
        }

        if ($endDay <= $lastDayTermCheck) {
            return ResponseBuilder::fail("Ngày kết thúc không hợp lệ vì nhỏ hơn ngày bắt đầu (" . $lastDayTermCheck->format('d/m/Y') . "), vui lòng thử lại!");
        }

        try {
            $term = new Term();
            $term->term_name = $termName;
            $term->ordering = $ordering + 1;
            $term->startday = $lastDayTermCheck->addDay(1)->format('Y-m-d');
            $term->endday = $endDay->format('Y-m-d');
            ActionLog::create([
                'object'        => 'term',
                'auth'          => auth()->user()->user_login,
                'action'        => 'create',
                'description'   => "Tài khoản " . auth()->user()->user_login . " tạo mới kỳ $termName",
                'object_id'     => ($parentUser->id ?? ""),
                'data_changed'  => json_encode([
                    'term_name' => $term->term_name,
                    'ordering'  => $term->ordering,
                    'startday'  => $term->startday,
                    'endday'    => $term->endday
                ]),
                'ip'            => $request->getClientIp(),
            ]);

            $term->save();
            return ResponseBuilder::Success(null, "Tạo mới kỳ $termName thành công!");
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::fail("Có lỗi xảy ra, vui lòng liên hệ với cán bộ IT");
        }

    }

    public function saveTermChange($request) {
        try {
            $rules = [
                'term_name' => 'required',
                'end_day' => 'required|date_format:Y-m-d',
            ];

            $messages = [
                'term_name.required' => 'Tên kỳ không được bỏ trống',
                'end_day.required' => 'Ngày kết thúc không được bỏ trống',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);
            if ($validator->fails()) {
                $mess = $validator->errors()->first();
                return ResponseBuilder::fail($mess);
            }

            $term_id = $request->id;
            $term_name = $request->term_name;
            $end_day = $request->end_day;

            // lấy kỳ cuối cùng và lấy ngày kết thúc của kỳ
            $term = Term::find($term_id);

            if (!$term) {
                return ResponseBuilder::fail("Không tìm thấy kỳ cần chỉnh sửa!", null, 500);
            }
            $term->term_name = $term_name;
            $term->endday = $end_day;

            if ($term->save()) {
                ActionLog::create([
                    'object'        => 'term',
                    'auth'          => auth()->user()->user_login,
                    'action'        => 'update',
                    'description'   => "Tài khoản " . auth()->user()->user_login . " chỉnh sửa kỳ $term_name",
                    'object_id'     => ($parentUser->id ?? ""),
                    'data_changed'  => json_encode([
                        'term_name' => $term->term_name,
                        'ordering'  => $term->ordering,
                        'startday'  => $term->startday,
                        'endday'    => $term->endday
                    ]),
                    'ip'            => $request->getClientIp(),
                ]);
                return ResponseBuilder::Success($term, "Thành công!");
            }

            return ResponseBuilder::fail("Chỉnh sửa thất bại!", null, 500);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::fail("Chỉnh sửa thất bại!", null, 500);
        }
    }

    public function getListKhoaNhapHoc() {
        try {
            $list = GradeCreate::select([
                "khoa_nhap_hoc.*",
                DB::raw('COUNT(user.id) as total_students'),
            ])
                ->leftJoin('user', 'khoa_nhap_hoc.khoa', '=', 'user.grade_create')
                ->groupBy('khoa_nhap_hoc.khoa')
                ->orderBy('ordering', 'desc')
                ->get();
            return ResponseBuilder::Success($list, "Thành công!");
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::fail("Thất bại!", null, 500);
        }
    }

    public function createKhoaNhapHoc(Request $request) {
        try {
            $validator = Validator::make($request->all(), [
                'khoa' => 'required|string|unique:khoa_nhap_hoc,khoa',
                'hoc_ky' => 'required|exists:term,id',
            ]);
            if ($validator->fails()) {
                $mess = $validator->errors()->first();
                return ResponseBuilder::fail($mess);
            }
            $term = Term::find($request->hoc_ky);
            $gradeCreate = new GradeCreate();
            $gradeCreate->khoa = $request->khoa;
            $gradeCreate->term_id = $term->id;
            $gradeCreate->hoc_ky = $term->term_name;
            $gradeCreate->ordering = GradeCreate::max('ordering') + 1;
            $gradeCreate->created_by = auth()->user()->user_login;
            $gradeCreate->save();
            ActionLog::create([
                'object'        => 'grade_create',
                'auth'          => auth()->user()->user_login,
                'action'        => 'create',
                'description'   => "Tài khoản " . auth()->user()->user_login . " tạo mới khóa nhập học $request->khoa",
                'object_id'     => ($parentUser->id ?? ""),
                'data_changed'  => json_encode([
                    'khoa' => $gradeCreate->khoa,
                    'hoc_ky' => $gradeCreate->hoc_ky
                ]),
                'ip'            => $request->getClientIp(),
            ]);
            return ResponseBuilder::Success($gradeCreate, "Tạo mới khóa nhập học thành công!");
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::fail("Thất bại!", null, 500);
        }
    }

    public function deleteKhoaNhapHoc(Request $request, $id) {
        try {
            $user_login = Auth::user()->user_login;
            $staff = User::where('user_login', $user_login)->where('user_level', 1)->first();
            if (!$staff) {
                return ResponseBuilder::fail("Không tìm thấy tài khoản có quyền!", null, 500);
            }

            $gradeCreate = GradeCreate::find($id);
            if (!$gradeCreate) {
                return ResponseBuilder::fail("Không tìm thấy khóa nhập học!", null, 500);
            }
            $gradeCreate->delete();
            ActionLog::create([
                'object'        => 'grade_create',
                'auth'          => auth()->user()->user_login,
                'action'        => 'delete',
                'description'   => "Tài khoản " . auth()->user()->user_login . " xóa khóa nhập học $gradeCreate->khoa",
                'object_id'     => "",
                'data_changed'  => json_encode([
                    'khoa' => $gradeCreate->khoa,
                    'hoc_ky' => $gradeCreate->hoc_ky
                ]),
                'ip'            => $request->getClientIp(),
            ]);
            return ResponseBuilder::Success(null, "Xóa khóa nhập học thành công!");
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::fail("Thất bại!", null, 500);
        }
    }

    public function getListTeacher(Request $request) {
        try {
            $list = User::select([
                'id',
                'user_code',
                'user_login',
                DB::raw('CONCAT(user.user_surname, " ", user.user_middlename, " ", user.user_givenname) as teacher_name'),
            ])
            ->where('user_level', 2)
            ->get();
            return ResponseBuilder::Success($list, "Thành công!");
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::fail("Thất bại!", null, 500);
        }
    }
}
