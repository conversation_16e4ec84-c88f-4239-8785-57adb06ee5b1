<?php

namespace App\Repositories\Admin;

use App\Models\Fu\ActionLog;
use App\Models\Fu\Department;
use App\Repositories\BaseRepository;
use App\Utils\ResponseBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class DepartmentRepository extends BaseRepository
{
    public function getModel()
    {
        return Department::class;
    }

    public function list(Request $request) {
        try {
            $departments = Department::select([
                'id',
                'department_name',
                'dean',
                'associate_dean',
                'customization'
            ])
            ->when($request->has('keyword'), function ($query) use ($request) {
                return $query->where('department_name', 'like', "%{$request->keyword}%");
            })
            ->orderBy('id')->get()->toArray();
            
            return ResponseBuilder::Success([
                'departments' => $departments
            ]);
        } catch (\Throwable $th) {
            Log::error("getDepartmentList at line : " . $th->getLine() . "\n" . $th->getMessage());
            return ResponseBuilder::Fail('Lỗi lấy danh sách bộ môn', null, 500);
        }
    }

    public function createDepartment(Request $request) {
        try {
            $rules = [
                'department_name' => 'required|string|max:255',
                'dean' => 'nullable|string|max:255',
                'associate_dean' => 'nullable|string|max:255',
                'customization' => 'nullable|string'
            ];

            $messages = [
                'department_name.required' => 'Tên bộ môn không được để trống.',
                'department_name.string' => 'Tên bộ môn phải là chuỗi.',
                'department_name.max' => 'Tên bộ môn không được vượt quá 255 ký tự.',
                'dean.string' => 'Chủ nhiệm bộ môn phải là chuỗi.',
                'dean.max' => 'Chủ nhiệm bộ môn không được vượt quá 255 ký tự.',
                'associate_dean.string' => 'Phó chủ nhiệm bộ môn phải là chuỗi.',
                'associate_dean.max' => 'Phó chủ nhiệm bộ môn không được vượt quá 255 ký tự.',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);

            if ($validator->fails()) {
                $errorMessages = [];
                foreach ($validator->errors()->all() as $error) {
                    $errorMessages[] = $error;
                }
                $errorString = implode("\n", $errorMessages);
                return ResponseBuilder::Fail($errorString, null, 422);
            }

            $departmentName = $request->input('department_name');
            $dean = $request->input('dean');
            $associateDean = $request->input('associate_dean');
            $customization = $request->input('customization');

            $checkDepartment = Department::where('department_name', $departmentName)->first();

            if ($checkDepartment) {
                return ResponseBuilder::Fail('Bộ môn đã tồn tại!', null, 400);
            }

            $department = Department::create([
                'department_name' => $departmentName,
                'dean' => $dean,
                'associate_dean' => $associateDean,
                'customization' => $customization
            ]);

            ActionLog::create([
                'object' => 'department',
                'auth' => auth()->user()->user_login,
                'action' => 'create',
                'description' => "Tài khoản " . auth()->user()->user_login . " tạo mới bộ môn $departmentName",
                'object_id' => $department->id,
                'data_changed' => json_encode([
                    'department_name' => $departmentName,
                    'dean' => $dean,
                    'associate_dean' => $associateDean,
                    'customization' => $customization
                ]),
                'ip' => $request->getClientIp(),
            ]);
            
            return ResponseBuilder::Success($department);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail('Lỗi tạo mới bộ môn', null, 500);
        }
    }
    
    public function updateDepartment(Request $request, $id)
    {
        try {
            $rules = [
                'id' => 'required|integer',
                'department_name' => 'required|string|max:255',
                'dean' => 'nullable|string|max:255',
                'associate_dean' => 'nullable|string|max:255',
                'customization' => 'nullable|string'
            ];

            $messages = [
                'department_name.required' => 'Tên bộ môn không được để trống',
                'department_name.string' => 'Tên bộ môn phải là chuỗi',
                'department_name.max' => 'Tên bộ môn không được vượt quá 255 ký tự',
                'dean.string' => 'Chủ nhiệm bộ môn phải là chuỗi',
                'dean.max' => 'Chủ nhiệm bộ môn không được vượt quá 255 ký tự',
                'associate_dean.string' => 'Phó chủ nhiệm bộ môn phải là chuỗi',
                'associate_dean.max' => 'Phó chủ nhiệm bộ môn không được vượt quá 255 ký tự',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);

            if ($validator->fails()) {
                $errorMessages = [];
                foreach ($validator->errors()->all() as $error) {
                    $errorMessages[] = $error;
                }
                $errorString = implode("\n", $errorMessages);
                return ResponseBuilder::Fail($errorString, null, 422);
            }

            $department = Department::find($id);

            if (!$department) {
                return ResponseBuilder::Fail('Không tìm thấy bộ môn', null, 404);
            }

            $oldData = [
                'department_name' => $department->department_name,
                'dean' => $department->dean,
                'associate_dean' => $department->associate_dean,
                'customization' => $department->customization
            ];

            $departmentName = $request->input('department_name');
            $dean = $request->input('dean');
            $associateDean = $request->input('associate_dean');
            $customization = $request->input('customization');

            $checkDepartment = Department::where('department_name', $departmentName)
                ->where('id', '!=', $id)
                ->first();

            if ($checkDepartment) {
                return ResponseBuilder::Fail('Bộ môn đã tồn tại', null, 400);
            }

            $department->update([
                'department_name' => $departmentName,
                'dean' => $dean,
                'associate_dean' => $associateDean,
                'customization' => $customization
            ]);

            ActionLog::create([
                'object' => 'department',
                'auth' => auth()->user()->user_login,
                'action' => 'update',
                'description' => "Tài khoản " . auth()->user()->user_login . " cập nhật bộ môn $departmentName",
                'object_id' => $department->id,
                'data_changed' => json_encode([
                    'old' => $oldData,
                    'new' => [
                        'department_name' => $departmentName,
                        'dean' => $dean,
                        'associate_dean' => $associateDean,
                        'customization' => $customization
                    ]
                ]),
                'ip' => $request->getClientIp(),
            ]);
            
            return ResponseBuilder::Success($department);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail('Lỗi cập nhật bộ môn', null, 500);
        }
    }

    public function deleteDepartment(Request $request, $id)
    {
        try {
            $department = Department::find($id);

            if (!$department) {
                return ResponseBuilder::Fail('Không tìm thấy bộ môn', null, 404);
            }

            $departmentName = $department->department_name;

            $department->delete();

            ActionLog::create([
                'object' => 'department',
                'auth' => auth()->user()->user_login,
                'action' => 'delete',
                'description' => "Tài khoản " . auth()->user()->user_login . " xóa bộ môn $departmentName",
                'object_id' => $id,
                'data_changed' => json_encode([
                    'department_name' => $departmentName,
                    'dean' => $department->dean,
                    'associate_dean' => $department->associate_dean,
                    'customization' => $department->customization
                ]),
                'ip' => $request->getClientIp(),
            ]);
            
            return ResponseBuilder::Success(null, 'Xóa bộ môn thành công');
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail('Lỗi xóa bộ môn', null, 500);
        }
    }

    public function updateDean(Request $request, $id)
    {
        try {
            $rules = [
                'dean' => 'required|string|max:255'
            ];

            $messages = [
                'dean.required' => 'Chủ nhiệm bộ môn không được để trống',
                'dean.string' => 'Chủ nhiệm bộ môn phải là chuỗi',
                'dean.max' => 'Chủ nhiệm bộ môn không được vượt quá 255 ký tự',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);

            if ($validator->fails()) {
                $errorMessages = [];
                foreach ($validator->errors()->all() as $error) {
                    $errorMessages[] = $error;
                }
                $errorString = implode("\n", $errorMessages);
                return ResponseBuilder::Fail($errorString, null, 422);
            }

            $department = Department::find($id);

            if (!$department) {
                return ResponseBuilder::Fail('Không tìm thấy bộ môn', null, 404);
            }

            $oldDean = $department->dean;
            $newDean = $request->input('dean');

            $department->update([
                'dean' => $newDean
            ]);

            ActionLog::create([
                'object' => 'department',
                'auth' => auth()->user()->user_login,
                'action' => 'update_dean',
                'description' => "Tài khoản " . auth()->user()->user_login . " cập nhật chủ nhiệm bộ môn " . $department->department_name,
                'object_id' => $department->id,
                'data_changed' => json_encode([
                    'old_dean' => $oldDean,
                    'new_dean' => $newDean
                ]),
                'ip' => $request->getClientIp(),
            ]);
            
            return ResponseBuilder::Success($department);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail('Lỗi cập nhật chủ nhiệm bộ môn', null, 500);
        }
    }

    public function updateAssociateDean(Request $request, $id)
    {
        try {
            $rules = [
                'associate_dean' => 'required|string|max:255'
            ];

            $messages = [
                'associate_dean.required' => 'Phó chủ nhiệm bộ môn không được để trống',
                'associate_dean.string' => 'Phó chủ nhiệm bộ môn phải là chuỗi',
                'associate_dean.max' => 'Phó chủ nhiệm bộ môn không được vượt quá 255 ký tự',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);

            if ($validator->fails()) {
                $errorMessages = [];
                foreach ($validator->errors()->all() as $error) {
                    $errorMessages[] = $error;
                }
                $errorString = implode("\n", $errorMessages);
                return ResponseBuilder::Fail($errorString, null, 422);
            }

            $department = Department::find($id);

            if (!$department) {
                return ResponseBuilder::Fail('Không tìm thấy bộ môn', null, 404);
            }

            $oldAssociateDean = $department->associate_dean;
            $newAssociateDean = $request->input('associate_dean');

            $department->update([
                'associate_dean' => $newAssociateDean
            ]);

            ActionLog::create([
                'object' => 'department',
                'auth' => auth()->user()->user_login,
                'action' => 'update_associate_dean',
                'description' => "Tài khoản " . auth()->user()->user_login . " cập nhật phó chủ nhiệm bộ môn " . $department->department_name,
                'object_id' => $department->id,
                'data_changed' => json_encode([
                    'old_associate_dean' => $oldAssociateDean,
                    'new_associate_dean' => $newAssociateDean
                ]),
                'ip' => $request->getClientIp(),
            ]);
            
            return ResponseBuilder::Success($department);
        } catch (\Throwable $th) {
            Log::error($th);
            return ResponseBuilder::Fail('Lỗi cập nhật phó chủ nhiệm bộ môn', null, 500);
        }
    }
}
