<div style="margin: auto;">
    <h1 style="text-align: center">THÔNG BÁO NỘP HỌC PHÍ KỲ FALL 2021</h1>
    {{-- <ul>
        <li style="font-weight: bold">SINH VIÊN VUI LÒNG BỎ QUA EMAIL TRƯỜNG HỢP ĐÃ NỘP HỌC PHÍ</li>
    </ul> --}}
    <table width="100%">
    <tr>
        <td>K<PERSON>h gửi sinh viên: <b>{{$full_name}}</b></td>
        <td>MSSV: {{$fee->user_code}}</td>
    </tr>
    </table>
    <h2 style="color: red;"><strong>Những sinh viên đã nộp học phí vui lòng bỏ qua mail này!</strong></h2>
    <p>Theo kế hoạch của phòng Quản lý Đào tạo, dự kiến, học kỳ Fall 2021 sẽ bắt đầu từ ngày 13/09/2021</p>
    <p>Phòng Hành chính gửi tới em số tiền học cần nộp kỳ Fall 2021 như sau:</p>
    <ul style="list-style-type: none">
        <li>1. Kỳ học dự kiến: {{$data->ki_thu}}</li>
        <li>2. Thông tin tài chính kỳ Fall 2021</li>
    </ul>
    <table width="100%" border="1" style="border-collapse: collapse;">
        @php
            $totalFee = $fee_detail['hoc_ky'] + $fee_detail['tien_sach'] + $fee_detail['tieng_anh'] - $fee_detail['study_wallet'];
        @endphp
        <tr>
            <th>Nội dung</th>
            <th>Số tiền (VNĐ)</th>
        </tr>
        {{-- <tr>
            <td><b>Học phí dự kiến</b></td>
            <td style="text-align: center"><b>{{number_format(array_sum(array_values($fee_detail)))}}</b></td>
        </tr> --}}
        @if (isset($fee_detail))
            @foreach (($fee_detail ?? []) as $key => $item)
                <tr>
                    <td>
                        @switch($key)
                            @case('study_wallet')
                                Thừa/thiếu học phí kỳ trước chuyển sang
                                @break
                            @case('hoc_ky')
                                Học phí
                                @break
                            @case('tien_sach')
                                Phí sách
                                @break
                            @case('tieng_anh')
                                Tiếng Anh
                                @break
                            @default
                        @endswitch    
                    </td>
                    <td style="text-align: center">{{number_format($item ?? 0)}}</td>
                </tr>
            @endforeach
                <tr>
                    <td> 
                        <b>Học phí SV cần nộp kỳ này (5 =1+2+3-4)</b>
                    </td>
                    <td style="text-align: center">{{number_format($totalFee ?? 0)}}</td>
                </tr>
        @endif
        {{-- <tr>
            <td>2</td>
            <td>Số tiền nhà trường hỗ trợ</td>
            <td style="text-align: center">0</td>
        </tr>
        <tr>
            <td>3</td>
            <td>Giải ngân (nếu có)</td>
            <td style="text-align: center">0</td>
        </tr>
        <tr>
            <td>4</td>
            <td>Học phí SV cần nộp kỳ này (4=1-2-3)</td>
            <td style="text-align: center">{{number_format($data->amount)}}</td>
        </tr> --}}
    </table>
    <ul style="list-style-type: none">
        <li>3. <b>Thời hạn nộp tiền: Từ 10/08/2021 đến 03/09/2021</b></li>
        <li>   Thời gian nhận sách từ 13/09/2021 đến 31/10/2021 tại Phòng Hành chính hoặc Nhà trường sẽ hỗ trợ CPN sách tới cho SV từ ngày 25/8 đến 13/9 (Chi phí người nhận trả)</li>
        <li>   Link đăng ký nhận sách tại nhà: <a href="https://bit.ly/3fCNtjS">https://bit.ly/3fCNtjS</a></li>
        <li>4. Hình thức nộp: Sinh viên lựa chọn các hình thức nộp tiền dưới đây:</li>
    </ul>
    <table width="100%" border="1" style="border-collapse: collapse;">
        <tr>
            <th>STT</th>
            <th>Hình thức nộp tiền</th>
            <th>Hướng dẫn chi tiết</th>
            <th>Lưu ý</th>
        </tr>
        <tr>
            <td>1</td>
            <td>QUA APP VIETTEL PAY</td>
            <td>Vào App ViettelPay => search => Chọn Thanh toán học phí => Nhập mã số SV => Thanh toán <b style="color: red">(Miễn Phí)</b></td>
            <td rowspan="3">Cách này nhanh, chính xác, tiết kiệm thời gian. Tiền sẽ được cập nhật lên hệ thống vào 12h ngày làm việc hôm sau.
            </td>
        </tr>
        <tr>
            <td>2</td>
            <td>NỘP TIỀN MẶT TẠI QUẦY GIAO DỊCH VIETTEL PAY</td>
            <td>Ra quầy đọc Mã số sinh viên => Nhân viên sẽ kiểm tra số tiền trên hóa đơn => Nộp tiền thanh toán hóa đơn đó (Phí 11.000đ/Lần)</td>
        </tr>
        <tr>
            <td>3</td>
            <td>Qua cổng thanh toán DNG: <a href="https://dngcorp.vn/Invoice" target="blank">https://dngcorp.vn/Invoice</a></td>
            <td>Truy cập link: <a href="https://dngcorp.vn/Invoice" target="blank">https://dngcorp.vn/Invoice</a>
                =>Nhập MSSV => Chọn phương thức thanh toán =>Nhập thông tin tài khoản thanh toán => Thanh toán
            </td>
        </tr>
        <tr>
            <td>4</td>
            <td>TPBANK.
                <ul>
                    <li>1. Mobile Ebanking TPBank</li>
                    <li>2. Internet Ebanking TPBank</li>
                </ul>
            </td>
            <td>Truy cập: Mobie APP: <b>TPBank Mobile</b> hoặc <a href="https://ebank.tpb.vn/" target="blank">https://ebank.tpb.vn/</a>
                =>Thanh toán Hóa đơn => Chọn mục Học phí- Lệ phí- => Học phí => Nhập MSSV => Thanh toán 
                <br> (Phí 5.000đ/Lần)
            </td>
            <td rowspan="2">Phải sau 1,5 ngày làm việc tiền cập nhật lên hệ thống của Nhà trường nên sinh viên nên hạn chế lựa chọn
            </td>
        </tr>
        <tr>
            <td>5</td>
            <td>BIDV.
                <ul>
                    <li>1. Mobile banking BIDV</li>
                    <li>2. Internet banking BIDV</li>
                </ul>
            </td>
            <td>Truy cập: Mobie APP: <b>SmartBanking </b> hoặc <a href="https://smartbanking.bidv.com.vn/dang-nhap" target="blank">https://smartbanking.bidv.com.vn/dang-nhap</a>
                =>Thanh toán hóa đơn => Chọn mục Học phí => Tổ chức giáo dục => Nhập MSSV => Thanh toán
                <br> (Phí 5.500đ/Lần)
            </td>
        </tr>
    </table>
    <ul>
        <li>Các trường hợp không nộp học phí đúng thời gian quy định được coi như tự nguyện thôi học.</li>
        <li>Sinh viên đã thôi học có nguyện vọng tiếp tục học phải làm thủ tục nhập học lại (Đơn xin nhập học lại + Phí nhập học lại : 500.000đ)</li>
        <li>SV có thắc mắc hoặc cần hỗ trợ thêm thông tin, vui lòng gọi ĐT số <b style="background-color: #fbff00;">024 7300 1955 nhánh số 3</b> hoặc <b style="background-color: #fbff00;">024 6682 2713</b> trong giờ hành chính để được hướng dẫn.</li>
    </ul>
    <b>Phòng Hành chính trân trọng thông báo.</b>
</div>
