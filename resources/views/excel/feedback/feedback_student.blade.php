<div>
    <h3>Tổng quan:	</h3>
    <table class="table table-striped">
        @php
            $res = [
                'a1' => 0,
                'a2' => 0,
                'a3' => 0,
                'a4' => 0,
            ];
            
            if ($infoReport['total'] != 0) {
                $res['a1'] = round(( ($infoReport['a1'] / $infoReport['total']) * 100 ), 2);
                $res['a2'] = round(( ($infoReport['a2'] / $infoReport['total']) * 100 ), 2);
                $res['a3'] = round(( ($infoReport['a3'] / $infoReport['total']) * 100 ), 2);
                $res['a4'] = round(( ($infoReport['a4'] / $infoReport['total']) * 100 ), 2);
            }
        @endphp
        <tr>
            <td colspan="2">Tổng số {{ $infoReport['total'] }} lượt tham gia đóng ý kiến</td>
        </tr>
        <tr>
            <td><PERSON><PERSON><PERSON></td>
            <td><PERSON><PERSON><PERSON> Q<PERSON></td>
        </tr>
        <tr>
            <td>RẤT HÀI LÒNG(trên 3.8)</td>
            <td>{{ $infoReport['a1'] }}/{{ $infoReport['total'] }} ({{ $res['a1'] }} %)</td>
        </tr>
        <tr>
            <td>HÀI LÒNG (3.6->3.8)</td>
            <td>{{ $infoReport['a2'] }}/{{ $infoReport['total'] }} ({{ $res['a2'] }} %)</td>
        </tr>
        <tr>
            <td>BÌNH THƯỜNG (3.4->3.6)</td>
            <td>{{ $infoReport['a3'] }}/{{ $infoReport['total'] }}({{ $res['a3'] }} %)</td>
        </tr>
        <tr>
            <td>DƯỚI BÌNH THƯỜNG (0->3.4)</td>
            <td>{{ $infoReport['a4'] }}/{{ $infoReport['total'] }} ({{ $res['a4'] }} %)</td>
        </tr>
    </table>
    <br>
    <h3>Chi tiết:	</h3>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>STT</th>
                <th>Login Giao Vien</th>
                <th>Tên giáo viên</th>
                <th>Lớp</th>
                <th>Bộ môn</th>
                <th>Mã Môn</th>
                <th>Tên Môn</th>
                <th>Q1</th>
                <th>Q2</th>
                <th>Q2</th>
                <th>Q4</th>
                <th>Q5</th>
                <th>GPA</th>
                <th>Số người lấy GPA</th>
                <th>Sĩ số</th>
                <th>Ý Kiến</th>
                <th>Ý Kiến (sau khi điều chỉnh)</th>
            </tr>
        </thead>
        <tbody class="border: 1px solid #000000">
            {{-- @foreach($datas as $key => $feedback)
                @php
                    $rowspan = count($feedback['list_comment']);
                @endphp
                @if ($rowspan == 0)
                    <tr>
                        <td>{{ $feedback['id'] }}</td>
                        <td>{{ $feedback['teacher_login'] }}</td>
                        <td>{{ $feedback['teacher_name'] }}</td>
                        <td>{{ $feedback['group_name'] }}</td>
                        <td>{{ $feedback['deparment'] }}</td>
                        <td>{{ $feedback['psubject_code'] }}</td>
                        <td>{{ $feedback['psubject_name'] }}</td>
                        <td>{{ $feedback['Q1'] }}</td>
                        <td>{{ $feedback['Q2'] }}</td>
                        <td>{{ $feedback['Q3'] }}</td>
                        <td>{{ $feedback['Q4'] }}</td>
                        <td>{{ $feedback['Q5'] }}</td>
                        <td>{{ $feedback['GPA'] }}</td>
                        <td>{{ $feedback['hit'] }}</td>
                        <td>{{ $feedback['total_member'] }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                @else
                    <tr>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['id'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['teacher_login'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['teacher_name'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['group_name'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['deparment'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['psubject_code'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['psubject_name'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['Q1'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['Q2'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['Q3'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['Q4'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['Q5'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['GPA'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['hit'] }}</td>
                        <td rowspan="{{ $rowspan }}">{{ $feedback['total_member'] }}</td>
                        <td>{{ $feedback['list_comment'][0]['comment'] }}</td>
                        <td>{{ $feedback['list_comment'][0]['edited_comment'] }}</td>
                    </tr>
                    @foreach ($feedback['list_comment'] as $k => $v)
                        @php
                            if ($k == 0) continue;
                        @endphp
                        <tr>
                            <td>{{ $v['comment'] }}</td>
                            <td>{{ $v['edited_comment'] }}</td>
                        </tr>
                    @endforeach
                @endif
            @endforeach --}}
            @foreach($datas as $key => $feedback)
                <tr>
                    <td>{{ $feedback['id'] }}</td>
                    <td>{{ $feedback['teacher_login'] }}</td>
                    <td>{{ $feedback['teacher_name'] }}</td>
                    <td>{{ $feedback['group_name'] }}</td>
                    <td>{{ $feedback['deparment'] }}</td>
                    <td>{{ $feedback['psubject_code'] }}</td>
                    <td>{{ $feedback['psubject_name'] }}</td>
                    <td>{{ $feedback['Q1'] }}</td>
                    <td>{{ $feedback['Q2'] }}</td>
                    <td>{{ $feedback['Q3'] }}</td>
                    <td>{{ $feedback['Q4'] }}</td>
                    <td>{{ $feedback['Q5'] }}</td>
                    <td>{{ $feedback['GPA'] }}</td>
                    <td>{{ $feedback['hit'] }}</td>
                    <td>{{ $feedback['total_member'] }}</td>
                    @if (count($feedback['list_comment']) > 0)
                        <td>
                            <ul>
                            @foreach ($feedback['list_comment'] as $item)
                                <li> * {{ ("\t" . $item['comment']) }}</li>
                            @endforeach
                            </ul>
                        </td>
                        <td>
                            <ul>
                            @foreach ($feedback['list_comment'] as $item)
                                <li> * {{ ("\t" . $item['edited_comment']) }}</li>
                            @endforeach
                            </ul>
                        </td>
                    @else
                        <td></td>
                        <td></td>
                    @endif
                </tr>
            @endforeach
        </tbody>
    </table>
    <style>
        td{
            font-family: 'Times New Roman', Times, serif;
        }
    </style>
</div>