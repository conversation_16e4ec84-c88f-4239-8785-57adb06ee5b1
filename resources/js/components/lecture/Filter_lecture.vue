<template>
    <div>
        <div class="row">
            <div class="col-sm-6 col-md-6 form-group mb-0 pr-0">
                <div class="input-group">
                    <b-form-datepicker id="start_date_attendance" v-model="searchData.start_date" class="mb-2" placeholder="<PERSON><PERSON>y bắt đầu - start date"></b-form-datepicker>
                </div>
            </div>
            <div class="col-sm-6 col-md-6 form-group mb-0 pr-0">
                <div class="input-group">
                    <b-form-datepicker id="end_date_attendance" v-model="searchData.end_date" class="mb-2" placeholder="<PERSON><PERSON>y kết thúc - end date"></b-form-datepicker>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4 col-md-4 form-group mb-0 pr-0">
                <select class="form-control" v-model="searchData.department">
                    <option :value="null" selected>Tất cả - Bộ môn</option>
                    <option v-for="department in list_department_lesson" :value="department.id">
                        {{ department.department_name }}
                    </option>
                </select>
            </div>
            <div class="col-sm-4 col-md-4 form-group mb-0 pr-0">
                <select class="form-control" id="option-subject_code" v-model="searchData.subject_code">
                    <option value="null" selected>Tất cả - Mã môn</option>
                    <option v-for="subject_code in list_subject_code" :value="subject_code.subject_code">
                        {{ subject_code.subject_code }} - {{ subject_code.subject_name }}
                    </option>
                </select>
            </div>
            <div class="col-sm-4 col-md-4 form-group mb-0 pr-0">
                <select class="form-control" id="option-teacher" v-model="searchData.teacher">
                    <option value="null" selected>Tất cả - Giảng viên</option>
                    <option v-for="teacher_lesson in list_teacher_lesson" :value="teacher_lesson.user_login">
                        {{ teacher_lesson.user_login }}
                    </option>
                </select>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'FilterLecture',
    props: {
        searchData: {
            type: Object,
            require: true,
            default: {},
        },
        list_department_lesson: {
            type: Array,
            require: true,
            default: [],
        },
        list_teacher_lesson: {
            type: Array,
            require: true,
            default: [],
        },
        list_subject_code: {
            type: Array,
            require: true,
            default: [],
        }
    },
    mounted() {
        var thiss = this;
        $('#option-teacher').select2({
            placeholder: 'Tất cả - Giảng viên',
            width: '100%',
        }).change(() => {
            thiss.optionDepartmentChange();
        });

        $('#option-subject_code').select2({
            placeholder: 'Tất cả - Mã môn',
            width: '100%',
        }).change(() => {
            thiss.optionSubjectCodeChange();
        });
    },
    methods: {
        optionDepartmentChange() {
            this.searchData.teacher = $('#option-teacher').val();
        },
        optionSubjectCodeChange() {
            this.searchData.subject_code = $('#option-subject_code').val();
        }
    }
}
</script>
