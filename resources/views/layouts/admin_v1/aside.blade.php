@php
    const MENU_LEVEL_1 = 1;
    const MENU_LEVEL_2 = 2;

    const LINK_DEFAULT = 'javascript:;';

    const ICON_1 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-graduation-cap-icon lucide-graduation-cap">
         <path d="M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z" />
         <path d="M22 10v6" />
         <path d="M6 12.5V16a6 3 0 0 0 12 0v-3.5" />
     </svg>';

    const ICON_2 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-check-icon lucide-clipboard-check">
         <rect width="8" height="4" x="8" y="2" rx="1" ry="1" />
         <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
         <path d="m9 14 2 2 4-4" />
     </svg>';


    const ICON_3 = '<svg xmlns="http://www.w3.org/2000/svg" style="cursor: pointer" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-check-icon lucide-clipboard-check">
        <path d="M10.268 21a2 2 0 0 0 3.464 0"/>
        <path d="M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"/>
    </svg>';

    const ICON_4 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hand-coins-icon lucide-hand-coins">
         <path d="M11 15h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 17" />
         <path d="m7 21 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9" />
         <path d="m2 16 6 6" />
         <circle cx="16" cy="9" r="2.9" />
         <circle cx="6" cy="5" r="3" />
     </svg>';

    const ICON_5 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square-more-icon lucide-message-square-more">
         <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
         <path d="M8 10h.01" />
         <path d="M12 10h.01" />
         <path d="M16 10h.01" />
     </svg>';

    const ICON_6 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-scroll-text-icon lucide-scroll-text">
         <path d="M15 12h-5" />
         <path d="M15 8h-5" />
         <path d="M19 17V5a2 2 0 0 0-2-2H4" />
         <path d="M8 21h12a2 2 0 0 0 2-2v-1a1 1 0 0 0-1-1H11a1 1 0 0 0-1 1v1a2 2 0 1 1-4 0V5a2 2 0 1 0-4 0v2a1 1 0 0 0 1 1h3" />
     </svg>';

     const ICON_7 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis-icon lucide-ellipsis"><circle cx="12" cy="12" r="1"/><circle cx="19" cy="12" r="1"/><circle cx="5" cy="12" r="1"/></svg>';

    const ICON_8 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-pen-icon lucide-clipboard-pen">
         <rect width="8" height="4" x="8" y="2" rx="1" />
         <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-5.5" />
         <path d="M4 13.5V6a2 2 0 0 1 2-2h2" />
         <path d="M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z" />
     </svg>';

    const ICON_9 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-handshake-icon lucide-handshake">
         <path d="m11 17 2 2a1 1 0 1 0 3-3" />
         <path d="m14 14 2.5 2.5a1 1 0 1 0 3-3l-3.88-3.88a3 3 0 0 0-4.24 0l-.88.88a1 1 0 1 1-3-3l2.81-2.81a5.79 5.79 0 0 1 7.06-.87l.47.28a2 2 0 0 0 1.42.25L21 4" />
         <path d="m21 3 1 11h-2" />
         <path d="M3 3 2 14l6.5 6.5a1 1 0 1 0 3-3" />
         <path d="M3 4h8" />
     </svg>';

    const ICON_10 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-stack-icon lucide-file-stack">
         <path d="M21 7h-3a2 2 0 0 1-2-2V2" />
         <path d="M21 6v6.5c0 .8-.7 1.5-1.5 1.5h-7c-.8 0-1.5-.7-1.5-1.5v-9c0-.8.7-1.5 1.5-1.5H17Z" />
         <path d="M7 8v8.8c0 .3.2.6.4.8.2.2.5.4.8.4H15" />
         <path d="M3 12v8.8c0 .3.2.6.4.8.2.2.5.4.8.4H11" />
     </svg>';

    const ICON_11 = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-plus">
        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
        <circle cx="9" cy="7" r="4"/>
        <line x1="19" y1="8" x2="19" y2="14"/>
        <line x1="22" y1="11" x2="16" y2="11"/>
    </svg>';

    $aside = [
        [
            'name' => 'Quản lý tuyển sinh',
            'link' => route('admin.admission.index'),
            'level' => MENU_LEVEL_1,
            'icon' => ICON_11,
            'hidden' => false,
            'roles' => [-1],
            'child' => [
                [
                    'level' => MENU_LEVEL_2,
                    'link' => route('admin.admission.index'),
                    'name' => 'Danh sách tuyển sinh',
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'level' => MENU_LEVEL_2,
                    'link' => route('admin.administrative_class.index'),
                    'name' => 'Danh sách lớp nhập học',
                    'hidden' => false,
                    'roles' => [-1],
                ],
            ],
        ],
        [
            'name' => 'Khung & chương trình đào tạo',
            'link' => LINK_DEFAULT,
            'level' => MENU_LEVEL_1,
            'icon' => ICON_10,
            'hidden' => false,
            'roles' => [-1],
            'child' => [
                [
                    'name' => 'Bộ môn',
                    'link' => route('admin.training_frame.departments'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Ngành học',
                    'link' => route('admin.training_frame.brands'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Khung đào tạo',
                    'link' => route('admin.training_frame.list_frame'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Sinh viên theo khung',
                    'link' => route('admin.training_frame.list_student_frame'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => true,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Quản lý môn học',
                    'link' => route('admin.syllabus.list'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Danh sách khóa học',
                    'link' => route('admin.course.index'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Ma trận môn thay thế',
                    'link' => route('admin.subject_update_able.index'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
            ],
        ],
        [
            'name' => 'TC & QL đào tạo',
            'link' => LINK_DEFAULT,
            'level' => MENU_LEVEL_1,
            'icon' => ICON_1,
            'hidden' => false,
            'roles' => [-1],
            'child' => [
                [
                    'name' => 'Môn học',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => true,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Danh sách môn học',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Môn tiên quyết',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Quản lý môn học/lịch trình học',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Khung chương trình',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => true,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Danh sách khung chương trình',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Cập nhật khung chương tình cho SV',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Quản lý khóa học',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Báo cáo',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => true,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Báo cáo đào tạo',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Báo cáo sinh viên kỳ',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Báo cáo học phí',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Export data CTSV',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Export data group CTSV',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Quản lý lớp môn',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.group.index'),
                            'name' => 'Danh sách lớp môn',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.group.manager_students'),
                            'name' => 'Cập nhật danh sách sinh viên theo lớp',
                            'hidden' => false,
                            'roles' => [-1],
                        ],

                        // [
                        //     'link' => route('admin.remove_student.remove_student_30'),
                        //     'name' => 'Rút lớp 30%',
                        //     'hidden' => false,
                        //     'roles' => [-1],
                        // ],
                        // [
                        //     'link' => route('admin.remove_student.remove_student_ap'),
                        //     'name' => 'Xóa sinh viên khỏi ap',
                        //     'hidden' => false,
                        //     'roles' => [-1],
                        // ],
                        // [
                        //     'link' => route('admin.gdqp.index'),
                        //     'name' => 'Quản lý GDQP',
                        //     'hidden' => false,
                        //     'roles' => [-1],
                        // ],
                    ],
                ],
                [
                    'name' => 'Trạng thái sinh viên',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.dropout'),
                            'name' => 'Cập nhật trạng thái sinh viên',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.student-status-logs.index'),
                            'name' => 'Lịch sử thay đổi trạng thái sinh viên',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'name' => 'Quản lý lên kỳ',
                            'link' => route('admin.promted-semester.index'),
                            'level' => MENU_LEVEL_2,
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Quản lý học tập',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.edu.thieu_no_mon'),
                            'name' => 'Kiểm tra thiếu nợ môn',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.export.detailed_lessons'),
                            'name' => 'Lượt học chi tiết',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.export.form_english_detail'),
                            'name' => 'Dữ liệu tiếng anh',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'name' => 'Học phần thay thế',
                            'link' => route('admin.alternative_subject_student.index'),
                            'level' => MENU_LEVEL_2,
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'name' => 'Học phần miễn giảm',
                            'link' => route('admin.subject_exemptions.list'),
                            'level' => MENU_LEVEL_2,
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Tình hình đi học',
                    'link' => route('tinh_hinh_di_hoc'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Quản lý đánh giá',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.check_feedback_block.getGroup'),
                            'name' => 'Nhận xét của giảng viên',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.feedback'),
                            'name' => 'Kết quả khảo sát từ sinh viên',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.feedback.create'),
                            'name' => 'Tạo phiếu khảo sát',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => "",
                            'name' => 'Ngân hàng câu hỏi',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Thống kê giờ giảng',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.lecture.indexlectureLesson'),
                            'name' => 'Thống kê theo buổi học',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.lecture.indexlectureTeacher'),
                            'name' => 'Thống kê theo giảng viên',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Quản lý tốt nghiệp',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.graduation.campaign'),
                            'name' => 'Tốt nghiệp',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.diploma.index'),
                            'name' => 'Quản lý văn bằng THPT',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Import thực tập',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Import điểm lý thuyết thực hành',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Khác',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.user.print_form'),
                            'name' => 'In thẻ sinh viên',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Đặt lại mật khẩu',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.ebook.index'),
                            'name' => 'Quản lý EBook',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                    ],
                ],
            ],
        ],
        [
            'name' => 'Lịch',
            'link' => LINK_DEFAULT,
            'level' => MENU_LEVEL_1,
            'icon' => ICON_2,
            'hidden' => false,
            'roles' => [-1],
            'child' => [
                [
                    'link' => route('admin.activity.index'),
                    'level' => MENU_LEVEL_2,
                    'name' => 'Xem lịch hàng ngày',
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'link' => route('admin.export.schedule.show'),
                    'level' => MENU_LEVEL_2,
                    'name' => 'Xếp lịch',
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'link' => LINK_DEFAULT,
                    'name' => 'Đổi lịch sinh viên',
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.group.suitable_schedule'),
                            'name' => 'Tìm lớp phù hợp',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.group.manager_schedule'),
                            'name' => 'Đổi chéo lịch học',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'link' => route('admin.schedule.schedule_mangement'),
                    'level' => MENU_LEVEL_2,
                    'name' => 'Quản lý đổi lịch học',
                    'hidden' => true,
                    'roles' => [-1],
                ],
            ],
        ],
        [
            'name' => 'Khảo thí',
            'link' => LINK_DEFAULT,
            'level' => MENU_LEVEL_1,
            'icon' => ICON_2,
            'hidden' => false,
            'roles' => [-1],
            'child' => [
                [
                    'name' => 'Điểm',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.edu.so_diem'),
                            'name' => 'Sổ điểm',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('edu.fuge.index'),
                            'name' => 'Nhập điểm theo file',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Lịch thi',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.activity.lich_thi_giua_ky_cuoi_ky'),
                            'name' => 'Lịch thi giữa kỳ/cuối kỳ',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.activity.importExamForm'),
                            'name' => 'Tải lên lịch thi',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Bảng điểm theo kỳ',
                    'link' => route('admin.grade_view.index'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Busảng điểm theo kỳ',
                    'link' => route('admin.grade_view.grade-by-term'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => true,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Danh sách SV bị đình chỉ thi/Huỷ KQ thi',
                    'link' => route('admin.discipline_exam_regulation'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
            ],
        ],
        [
            'name' => 'Tài chính sinh viên',
            'link' => LINK_DEFAULT,
            'level' => MENU_LEVEL_1,
            'icon' => ICON_4,
            'hidden' => false,
            'roles' => [-1],
            'child' => [
                [
                    'name' => 'Biểu phí môn học',
                    'link' => route('admin.fee.subject_manage'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Tạo đợt thu học phí',
                    'link' => route('admin.fee.tuition_period'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Quản lý công nợ',
                    'link' => route('admin.debt.list'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Ví sinh viên',
                    'link' => route('admin.wallet.list'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
            ],
        ],
        [
            'name' => 'Chăm sóc sinh viên',
            'link' => LINK_DEFAULT,
            'level' => MENU_LEVEL_1,
            'icon' => ICON_5,
            'hidden' => false,
            'roles' => [-1],
            'child' => [
                [
                    'name' => 'Quản lý tin nhắn',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.sms.index'),
                            'name' => 'Quản lý gửi tin nhắn',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.sms.format_sms'),
                            'name' => 'Quản lý mẫu tin nhắn',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Lịch sử tin nhắn theo yêu cầu',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Quản lý tài khoản',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Đăng ký tài khonar mới',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Import tài khoản hàng loạt',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Gửi tin nhắn lớp',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Gửi tin nhắn (CSV)',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Gửi tin nhắn học phí (CSV) - NEW',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Duyệt tin nhắn gửi đi',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Chăm sóc sinh viên',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                        [
                            'link' => LINK_DEFAULT,
                            'name' => 'Lịch sử tin nhắn chủ động',
                            'hidden' => true,
                            'roles' => [-1],
                        ],
                    ],
                ],
                [
                    'name' => 'Danh sách sinh viên bị cảnh báo',
                    'link' => route('admin.sms.student_warning'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Phân quyền SMS',
                    'link' => route('admin.sms.role'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
            ],
        ],
        [
            'name' => 'Khác',
            'link' => LINK_DEFAULT,
            'level' => MENU_LEVEL_1,
            'icon' => ICON_7,
            'hidden' => false,
            'roles' => [-1],
            'child' => [
                [
                    'name' => 'Quản lý đăng ký BHYT',
                    'link' => route('tinh_hinh_di_hoc'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => true,
                    'roles' => [-1],
                    'child' => [
                        [
                            'name' => 'Quản lý đợt đăng ký BHYT',
                            'link' => route('admin.bhyt.available_periods'),
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'name' => 'Quản lý đơn đăng ký BHYT',
                            'link' => route('admin.bhyt.index'),
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'name' => 'Quản lý lịch sử xử lý BHYT',
                            'link' => route('admin.bhyt.available_student_logs'),
                            'hidden' => false,
                            'roles' => [-1],
                        ]
                    ]
                ],
                [
                    'name' => 'Học kỳ',
                    'link' => route('admin.other_manage.term'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Khu',
                    'link' => route('admin.other_manage.area'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => true,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Ca học',
                    'link' => route('admin.other_manage.slot'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Phòng học',
                    'link' => route('admin.other_manage.room'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Kiểm tra phòng trống',
                    'link' => route('admin.other_manage.check_room'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                'name' => 'Khoá nhập học',
                'link' => route('admin.other_manage.khoa_nhap_hoc'),
                'level' => MENU_LEVEL_2,
                'hidden' => false,
                'roles' => [-1],
                ],
            ],
        ],
        [
            'name' => 'Quản lý quyết định',
            'link' => route('admin.decision.index'),
            'level' => MENU_LEVEL_1,
            'icon' => ICON_8,
            'hidden' => false,
            'roles' => [-1],
        ],

        [
            'name' => 'Thông báo và hỏi đáp sinh viên',
            'link' => LINK_DEFAULT,
            'level' => MENU_LEVEL_1,
            'icon' => ICON_3,
            'hidden' => false,
            'roles' => [-1],
            'child' => [
                [
                    'name' => 'Hỗ trợ',
                    'link' => LINK_DEFAULT,
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                    'child' => [
                        [
                            'link' => route('admin.student_helper.category'),
                            'name' => 'Quản lý danh mục',
                            'hidden' => false,
                            'roles' => [-1],
                        ],
                        [
                            'link' => route('admin.student_helper.qna'),
                            'name' => 'Câu hỏi và trả lời',
                            'hidden' => false,
                            'roles' => [-1],
                        ],

                    ],
                ],
                [
                    'name' => 'Thông báo đặc biệt',
                    'link' => route('admin.popup.index'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Bảng tin sinh viên',
                    'link' => route('admin.newsletter_student.indexNewsletterStudent'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
            ],
        ],
        [
            'name' => 'Quản trị người dùng',
            'link' => LINK_DEFAULT,
            'level' => MENU_LEVEL_1,
            'icon' => ICON_6,
            'hidden' => false,
            'child' => [
                [
                    'name' => 'Quản lý người dùng',
                    'link' => route('admin.user'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Vai trò',
                    'link' => route('admin.role'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
                [
                    'name' => 'Quyền hạn',
                    'link' => route('admin.permission'),
                    'level' => MENU_LEVEL_2,
                    'hidden' => false,
                    'roles' => [-1],
                ],
            ],
        ],
    ];

    $aside = json_encode($aside);
    $aside = json_decode($aside, false);
    if (auth()->user()->user_level === 2) {
        $aside = [];
    }
    $url_current = url()->current();
@endphp
<div class="kt-aside-menu-wrapper kt-grid__item kt-grid__item--fluid" id="kt_aside_menu_wrapper" style="margin-top: 15px;">
    <div id="kt_aside_menu" class="kt-aside-menu" data-ktmenu-vertical="1" data-ktmenu-scroll="1"
        data-ktmenu-dropdown-timeout="500">
        <ul class="kt-menu__nav">
            @foreach ($aside as $data)
                @php
                    $is_open = false;
                    if ($data->link == $url_current) {
                        $is_open = true;
                    } elseif (isset($data->child)) {
                        foreach ($data->child as $child_lv2) {
                            if ($child_lv2->link == $url_current) {
                                $is_open = true;
                                break;
                            }
                            if (isset($child_lv2->child)) {
                                foreach ($child_lv2->child as $child_lv3) {
                                    if ($child_lv3->link == $url_current) {
                                        $is_open = true;
                                        break 2;
                                    }
                                }
                            }
                        }
                    }
                @endphp
                @php
                    $has_permission = true;
                    if (isset($data->roles)) {
                        if (in_array(-1, $data->roles)) {
                            $has_permission = true;
                        } else {
                            $user_roles = session('roles');
                            $has_permission = false;
                            foreach ($user_roles as $user_role) {
                                if (in_array($user_role, $data->roles)) {
                                    $has_permission = true;
                                    break;
                                }
                            }
                        }
                    }
                @endphp
                @if (
                    $data->level == MENU_LEVEL_1 &&
                    $has_permission &&
                    $data->hidden == false &&
                    (isset($data->campus) ? in_array(session('campus_db'), $data->campus) : true))

                    <li class="kt-menu__item {{ $is_open ? 'kt-menu__item--open kt-menu__item--here menu-parent-active' : '' }} {{ $data->link == $url_current ? 'kt-menu__item--active' : '' }}"

                        aria-haspopup="true" data-ktmenu-submenu-toggle="click">
                        <a href="{{ $data->link }}"
                            class="kt-menu__link hover-shrink
                                    {{ isset($data->child) ? 'kt-menu__toggle' : '' }}
                                    {{ $data->link == $url_current ? 'kt-menu__item--active' : '' }}">
                            <span class="kt-menu__link-icon">
                                {!! $data->icon !!}
                            </span>
                            <span class="kt-menu__link-text">{{ $data->name }}</span>
                            @if (isset($data->child))
                                <i class="kt-menu__ver-arrow la la-angle-right"></i>
                            @endif
                        </a>
                        <div class="kt-menu__submenu"><span class="kt-menu__arrow"></span>
                            <ul class="kt-menu__subnav">
                                <li class="kt-menu__item kt-menu__item--parent" aria-haspopup="true">
                                    <span class="kt-menu__link">
                                        <span class="kt-menu__link-text">{{ $data->name }}</span>
                                    </span>
                                </li>
                                @if (isset($data->child))
                                    @foreach ($data->child as $child)
                                        @php
                                            $child_has_permission = true;
                                            if (isset($child->roles)) {
                                                if (in_array(-1, $child->roles)) {
                                                    $child_has_permission = true;
                                                } else {
                                                    $user_roles = session('roles');
                                                    $child_has_permission = false;
                                                    foreach ($user_roles as $user_role) {
                                                        if (in_array($user_role, $child->roles)) {
                                                            $child_has_permission = true;
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                        @endphp
                                        @if (
                                            $child->level == MENU_LEVEL_2 &&
                                            $child_has_permission &&
                                            $child->hidden == false &&
                                            (isset($child->campus) ? in_array(session('campus_db'), $child->campus) : true))
                                            @php
                                                $child_is_active = $child->link == $url_current;
                                                if (isset($child->child)) {
                                                    foreach ($child->child as $subchild) {
                                                        if ($subchild->link == $url_current) {
                                                            $child_is_active = true;
                                                            break;
                                                        }
                                                    }
                                                }
                                            @endphp
                                            <li class="kt-menu__item kt-menu__item--submenu {{ $child_is_active ? 'kt-menu__item--active kt-menu__item--open kt-menu__item--here' : '' }}"
                                                style="margin-top: 5px;"
                                                aria-haspopup="true" data-ktmenu-submenu-toggle="click">
                                                <a href="{{ $child->link }}"
                                                class="kt-menu__link hover-shrink {{ isset($child->child) ? 'kt-menu__toggle' : '' }} {{ $child->link == $url_current ? 'kt-menu__item--active' : '' }}">
                                                    <i class="kt-menu__link-bullet kt-menu__link-bullet--dot">
                                                        <span></span>
                                                    </i>
                                                    <span class="kt-menu__link-text">{{ $child->name }}</span>
                                                    @if (isset($child->child))
                                                        <i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                    @endif
                                                </a>
                                                <div class="kt-menu__submenu"><span class="kt-menu__arrow"></span>
                                                    <ul class="kt-menu__subnav">
                                                        <li class="kt-menu__item kt-menu__item--parent"
                                                            aria-haspopup="true">
                                                            <span class="kt-menu__link">
                                                                <span class="kt-menu__link-text">{{ $child->name }}</span>
                                                            </span>
                                                        </li>
                                                        @if (isset($child->child))
                                                            @foreach ($child->child as $child)
                                                                @php
                                                                    $child_has_permission = true;
                                                                    if (isset($child->roles)) {
                                                                        if (in_array(-1, $child->roles)) {
                                                                            $child_has_permission = true;
                                                                        } else {
                                                                            $user_roles = session('roles');
                                                                            $child_has_permission = false;
                                                                            foreach ($user_roles as $user_role) {
                                                                                if (in_array($user_role, $child->roles)) {
                                                                                    $child_has_permission = true;
                                                                                    break;
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                @endphp
                                                                @if (
                                                                    $child_has_permission &&
                                                                    $child->hidden == false &&
                                                                    (isset($child->campus) ? in_array(session('campus_db'), $child->campus) : true))
                                                                    <li class="kt-menu__item menu_level_2 {{ $child->link == $url_current ? 'kt-menu__item--active' : null }}"
                                                                        aria-haspopup="true" style="margin-top: 5px;">
                                                                        <a href="{{ $child->link }}"
                                                                            class="kt-menu__link hover-shrink">
                                                                            <i class="kt-menu__link-bullet kt-menu__link-bullet--dot">
                                                                                <span></span>
                                                                            </i>
                                                                            <span class="kt-menu__link-text">{{ $child->name }}</span>
                                                                        </a>
                                                                    </li>
                                                                @endif
                                                            @endforeach
                                                        @endif
                                                    </ul>
                                                </div>
                                            </li>
                                        @endif
                                    @endforeach
                                @endif
                            </ul>
                        </div>
                    </li>
                @endif
            @endforeach
        </ul>
    </div>
</div>

