<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repositories\Admin\GraduateRepository;

class GraduateController extends Controller
{
    /**
     * @var GraduateRepository
     */
    var $graduateRepository;

    public function __construct(GraduateRepository $graduateRepository)
    {
        $this->graduateRepository = $graduateRepository;
    }

    public function export_danh_sach_sinh_vien(Request $request)
    {
        return $this->graduateRepository->exportDanhSachSinhVien($request);
    }

    public function process_export_danh_sach_sinh_vien(Request $request)
    {
        return $this->graduateRepository->processExportDanhSachSinhVien($request);
    }

    public function xep_lich_bao_ve_sinh_vien(Request $request)
    {
        return $this->graduateRepository->xepLichBaoVeSinhVien($request);
    }

    public function process_import_lich_bao_ve_sinh_vien(Request $request)
    {
        return $this->graduateRepository->processImportLichBaoVeSinhVien($request);
    }

    public function export_danh_sach_giam_thi(Request $request)
    {
        return $this->graduateRepository->exportDanhSachGiamThi($request);
    }

    public function process_export_danh_sach_giam_thi(Request $request)
    {
        return $this->graduateRepository->processExportDanhSachGiamThi($request);
    }

    public function xep_giam_thi(Request $request)
    {
        return $this->graduateRepository->XepGiamThi($request);
    }

    public function process_import_xep_giam_thi(Request $request)
    {
        return $this->graduateRepository->processImportXepGiamThi($request);
    }
}
