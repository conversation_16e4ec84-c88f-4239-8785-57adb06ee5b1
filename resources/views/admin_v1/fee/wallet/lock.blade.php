@extends('layouts.admin_v1.main')

@section('title', 'Khóa ví sinh viên')

@section('content')
<div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
    <div class="row">
        <div class="col-lg-12">
            <!--begin::Portlet-->
            <div class="kt-portlet">
                <div class="kt-portlet__head">
                    <div class="kt-portlet__head-label">
                        <h3 class="kt-portlet__head-title">
                            Khóa ví sinh viên
                        </h3>
                    </div>
                </div>
                <!--begin::Form-->
                <form class="kt-form kt-form--label-right" method="POST" action="{{ route('admin.wallet.lock', $wallet->id) }}">
                    @csrf
                    <div class="kt-portlet__body">
                        <div class="kt-section kt-section--first">
                            <div class="kt-section__body">
                                <div class="form-group row">
                                    <label class="col-xl-3 col-lg-3 col-form-label">Mã sinh viên:</label>
                                    <div class="col-lg-9 col-xl-6">
                                        <input type="text" class="form-control" value="{{ $wallet->user_code }}" disabled>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-xl-3 col-lg-3 col-form-label">Tài khoản:</label>
                                    <div class="col-lg-9 col-xl-6">
                                        <input type="text" class="form-control" value="{{ $wallet->user_login }}" disabled>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-xl-3 col-lg-3 col-form-label">Số dư hiện tại:</label>
                                    <div class="col-lg-9 col-xl-6">
                                        <input type="text" class="form-control" value="{{ number_format($wallet->balance) }} VNĐ" disabled>
                                        <span class="form-text text-muted">Số dư hiện tại trong ví sinh viên</span>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-xl-3 col-lg-3 col-form-label">Trạng thái hiện tại:</label>
                                    <div class="col-lg-9 col-xl-6">
                                        @if($wallet->is_locked)
                                            <span class="badge badge-danger">🔒 Đã khóa</span>
                                            @if($wallet->lock_reason)
                                                <br><small class="text-muted mt-2">Lý do: {{ $wallet->lock_reason }}</small>
                                            @endif
                                        @else
                                            <span class="badge badge-success">✅ Hoạt động</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-xl-3 col-lg-3 col-form-label">Ngày tạo:</label>
                                    <div class="col-lg-9 col-xl-6">
                                        <input type="text" class="form-control" value="{{ $wallet->created_at->format('d/m/Y H:i') }}" disabled>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-xl-3 col-lg-3 col-form-label">Lý do khóa <span class="text-danger">*</span></label>
                                    <div class="col-lg-9 col-xl-6">
                                        <textarea class="form-control @error('reason') is-invalid @enderror" name="reason" rows="3" placeholder="Nhập lý do khóa ví" required></textarea>
                                        @error('reason')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="kt-portlet__foot">
                        <div class="kt-form__actions">
                            <div class="row">
                                <div class="col-lg-3 col-xl-3"></div>
                                <div class="col-lg-9 col-xl-9">
                                    <button type="submit" class="btn btn-danger">Khóa ví</button>
                                    <a href="{{ route('admin.wallet.list') }}" class="btn btn-secondary">Hủy</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!--end::Form-->
            </div>
            <!--end::Portlet-->
        </div>
    </div>
</div>
@endsection
