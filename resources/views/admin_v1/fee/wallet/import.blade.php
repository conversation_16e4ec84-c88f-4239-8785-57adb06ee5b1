@extends('layouts.admin_v1.main')

@section('title', $action === 'create' ? 'Import tạo ví sinh viên hàng loạt' : 'Import nạp tiền hàng loạt')

@section('content')
<div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
    <div class="row">
        <div class="col-lg-12">
            <!--begin::Portlet-->
            <div class="kt-portlet">
                <div class="kt-portlet__head">
                    <div class="kt-portlet__head-label">
                        <span class="kt-portlet__head-icon">
                            @if($action === 'create')
                                <i class="flaticon2-plus kt-font-success"></i>
                            @else
                                <i class="flaticon2-plus kt-font-warning"></i>
                            @endif
                        </span>
                        <h3 class="kt-portlet__head-title">
                            @if($action === 'create')
                                Import tạo ví sinh viên hàng loạt
                                <span class="kt-portlet__head-desc">
                                    Tạo ví điện tử cho nhiều sinh viên cùng lúc
                                </span>
                            @else
                                Import nạp tiền hàng loạt
                                <span class="kt-portlet__head-desc">
                                    Nạp tiền vào ví cho nhiều sinh viên cùng lúc
                                </span>
                            @endif
                        </h3>
                    </div>
                    <div class="kt-portlet__head-toolbar">
                        <div class="kt-portlet__head-wrapper">
                            <div class="kt-portlet__head-actions">
                                <a href="{{ route('admin.wallet.import.template', ['type' => $action]) }}" class="btn btn-info btn-elevate btn-icon-sm" data-toggle="tooltip" title="Tải file Excel mẫu">
                                    <i class="la la-download"></i>
                                    Tải template Excel
                                </a>
                                <a href="{{ route('admin.wallet.list') }}" class="btn btn-clean btn-elevate btn-icon-sm">
                                    <i class="la la-arrow-left"></i>
                                    Quay lại
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Form-->
                <form class="kt-form kt-form--label-right" method="POST" action="{{ route('admin.wallet.import.process') }}" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="action" value="{{ $action }}">

                    <div class="kt-portlet__body">
                        <!-- Instructions -->
                        <div class="alert alert-info">
                            <div class="kt-alert__icon">
                                <i class="flaticon-info"></i>
                            </div>
                            <div class="kt-alert__text">
                                <strong>Hướng dẫn:</strong><br>
                                @if($action === 'create')
                                    1. Tải template Excel bằng cách nhấn nút "Tải template" ở trên<br>
                                    2. Điền thông tin sinh viên vào file Excel theo mẫu<br>
                                    3. Chọn file và nhấn "Import" để tạo ví hàng loạt<br>
                                    4. Hệ thống sẽ kiểm tra user_level = 3 (sinh viên) và tạo ví với số dư = 0<br>
                                    5. <strong>Lưu ý:</strong> Mỗi sinh viên chỉ có 1 ví duy nhất
                                @else
                                    1. Tải template Excel bằng cách nhấn nút "Tải template" ở trên<br>
                                    2. Điền thông tin nạp tiền vào file Excel theo mẫu<br>
                                    3. Chọn file và nhấn "Import" để nạp tiền hàng loạt<br>
                                    4. Sinh viên phải đã có ví trước khi nạp tiền<br>
                                    5. <strong>Lưu ý:</strong> Số tiền tối thiểu là 1,000 VNĐ
                                @endif
                            </div>
                        </div>

                        <!-- File Upload -->
                        <div class="form-group row">
                            <label class="col-form-label col-lg-3 col-sm-12">Chọn file Excel <span class="text-danger">*</span></label>
                            <div class="col-lg-6 col-md-9 col-sm-12">
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input @error('file') is-invalid @enderror" id="file" name="file" accept=".xlsx,.xls,.csv" required>
                                    <label class="custom-file-label" for="file">Chọn file...</label>
                                </div>
                                @error('file')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <span class="form-text text-muted">Chỉ chấp nhận file .xlsx, .xls, .csv (tối đa 10MB)</span>
                            </div>
                        </div>

                        <!-- Sample Data Preview -->
                        <div class="kt-separator kt-separator--border-dashed kt-separator--space-lg"></div>

                        <h5 class="kt-margin-b-20">
                            <i class="flaticon2-list-2"></i>
                            Định dạng dữ liệu mẫu
                        </h5>

                        @if($action === 'create')
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>ma_sinh_vien</th>
                                        <th>tai_khoan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>SV001</td>
                                        <td>student001</td>
                                    </tr>
                                    <tr>
                                        <td>SV002</td>
                                        <td>student002</td>
                                    </tr>
                                    <tr>
                                        <td>SV003</td>
                                        <td>student003</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Validation Info -->
                        <div class="alert alert-warning kt-margin-t-20">
                            <h6><strong>Validation kiểm tra:</strong></h6>
                            <ul class="kt-margin-b-0">
                                <li>✅ Mã sinh viên và tài khoản phải tồn tại trong hệ thống</li>
                                <li>✅ Mã sinh viên và tài khoản phải thuộc cùng 1 người</li>
                                <li>✅ Tài khoản phải có <code>user_level = 3</code> (sinh viên)</li>
                                <li>✅ Sinh viên chưa có ví trong hệ thống</li>
                                <li>✅ Ví được tạo với số dư = 0</li>
                            </ul>
                        </div>
                        @else
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>ma_sinh_vien</th>
                                        <th>so_tien</th>
                                        <th>mo_ta</th>
                                        <th>phuong_thuc</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>SV001</td>
                                        <td>1000000</td>
                                        <td>Nạp tiền học phí</td>
                                        <td>bank_transfer</td>
                                    </tr>
                                    <tr>
                                        <td>SV002</td>
                                        <td>500000</td>
                                        <td>Nạp tiền học lại</td>
                                        <td>cash</td>
                                    </tr>
                                    <tr>
                                        <td>SV003</td>
                                        <td>200000</td>
                                        <td>Nạp tiền phụ thu</td>
                                        <td>card</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Validation Info -->
                        <div class="alert alert-warning kt-margin-t-20">
                            <h6><strong>Validation kiểm tra:</strong></h6>
                            <ul class="kt-margin-b-0">
                                <li>✅ Sinh viên phải đã có ví trong hệ thống</li>
                                <li>✅ Ví không được bị khóa</li>
                                <li>✅ Số tiền >= 1,000 VNĐ</li>
                                <li>✅ Tiền sẽ được nạp vào ví duy nhất của sinh viên</li>
                            </ul>
                        </div>
                        @endif
                    </div>

                    <div class="kt-portlet__foot">
                        <div class="kt-form__actions">
                            <div class="row">
                                <div class="col-lg-3 col-md-3"></div>
                                <div class="col-lg-6 col-md-9">
                                    <button type="submit" class="btn btn-primary btn-elevate">
                                        <i class="la la-upload"></i>
                                        @if($action === 'create')
                                            Import tạo ví
                                        @else
                                            Import nạp tiền
                                        @endif
                                    </button>
                                    <a href="{{ route('admin.wallet.list') }}" class="btn btn-secondary">
                                        <i class="la la-arrow-left"></i>
                                        Quay lại
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!--end::Form-->
            </div>
            <!--end::Portlet-->

            <!-- Import Results -->
            @if(session('import_results'))
            <div class="kt-portlet">
                <div class="kt-portlet__head">
                    <div class="kt-portlet__head-label">
                        <h3 class="kt-portlet__head-title">
                            Kết quả Import
                        </h3>
                    </div>
                </div>
                <div class="kt-portlet__body">
                    @php $results = session('import_results'); @endphp

                    <div class="row">
                        <div class="col-md-3">
                            <div class="kt-widget24">
                                <div class="kt-widget24__details">
                                    <div class="kt-widget24__info">
                                        <h4 class="kt-widget24__title">Tổng số dòng</h4>
                                        <span class="kt-widget24__desc">Đã xử lý</span>
                                    </div>
                                    <span class="kt-widget24__stats kt-font-brand">{{ $results['total_rows'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="kt-widget24">
                                <div class="kt-widget24__details">
                                    <div class="kt-widget24__info">
                                        <h4 class="kt-widget24__title">Thành công</h4>
                                        <span class="kt-widget24__desc">Đã xử lý</span>
                                    </div>
                                    <span class="kt-widget24__stats kt-font-success">{{ $results['success_count'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="kt-widget24">
                                <div class="kt-widget24__details">
                                    <div class="kt-widget24__info">
                                        <h4 class="kt-widget24__title">Lỗi</h4>
                                        <span class="kt-widget24__desc">Không xử lý được</span>
                                    </div>
                                    <span class="kt-widget24__stats kt-font-danger">{{ $results['error_count'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="kt-widget24">
                                <div class="kt-widget24__details">
                                    <div class="kt-widget24__info">
                                        <h4 class="kt-widget24__title">Tỷ lệ thành công</h4>
                                        <span class="kt-widget24__desc">Phần trăm</span>
                                    </div>
                                    <span class="kt-widget24__stats kt-font-info">
                                        {{ $results['total_rows'] > 0 ? round(($results['success_count'] / $results['total_rows']) * 100, 1) : 0 }}%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if(!empty($results['errors']))
                    <div class="kt-separator kt-separator--border-dashed kt-separator--space-lg"></div>
                    <h5 class="kt-margin-b-20 text-danger">
                        <i class="flaticon-warning"></i>
                        Chi tiết lỗi
                    </h5>
                    <div class="alert alert-danger">
                        <ul class="kt-margin-b-0">
                            @foreach(array_slice($results['errors'], 0, 10) as $error)
                            <li>{{ $error }}</li>
                            @endforeach
                            @if(count($results['errors']) > 10)
                            <li><em>... và {{ count($results['errors']) - 10 }} lỗi khác</em></li>
                            @endif
                        </ul>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Custom file input label
    $('.custom-file-input').on('change', function() {
        let fileName = $(this).val().split('\\').pop();
        $(this).siblings('.custom-file-label').addClass('selected').html(fileName);
    });
});
</script>
@endsection
